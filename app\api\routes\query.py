"""
Query endpoints for the multi-agent system.
"""

import time
import asyncio
from typing import List, Optional, Dict, Any

from fastapi import API<PERSON>outer, Depends, HTTPException, BackgroundTasks
from pydantic import BaseModel

from app.api.models.pydantic_models import (
    QueryRequest, QueryResponse, QueryResult
)
from app.knowledge_base.embedding.embedding_client import EmbeddingClient
from app.knowledge_base.pinecone.pinecone_client import PineconeClient
from app.agents.coordinator import CoordinatorAgent
from app.core.logging import get_logger

router = APIRouter(tags=["Query"])
logger = get_logger("api.query")

# Initialize clients
embedding_client = EmbeddingClient()
pinecone_client = PineconeClient()

@router.post("/query", response_model=Dict[str, Any])
async def process_query(
    request: QueryRequest,
    background_tasks: BackgroundTasks = None
):
    """
    Process a query through the multi-agent system.
    
    Args:
        request: Query request.
        background_tasks: Background tasks.
        
    Returns:
        Dict[str, Any]: Query response.
    """
    start_time = time.time()
    logger.info(f"Query requested: {request.query}")
    
    try:
        # Generate embedding for query
        query_embedding = await embedding_client.generate_embedding(request.query)
        
        # Search Pinecone for relevant context
        results = await pinecone_client.query_vectors(
            query_embedding=query_embedding,
            top_k=request.max_results,
            filter=None
        )
        
        # Initialize coordinator agent
        coordinator = CoordinatorAgent()
        
        # Process query with coordinator agent
        response = await coordinator.process_query(
            query=request.query,
            context=results,
            user_context=request.user_context
        )
        
        processing_time = time.time() - start_time
        logger.info(f"Query processed in {processing_time:.2f}s")
        
        # Format sources for response
        sources = []
        for result in results:
            # Extract content and truncate if needed
            content = result.content
            if len(content) > 200:
                content = content[:200] + "..."

            sources.append({
                "document_id": result.document_id,
                "content": content,
                "metadata": {
                    "filename": getattr(result.metadata, 'title', getattr(result.metadata, 'source', 'Unknown')),
                    "page": getattr(result.metadata, 'page', 1),
                    "chunk_id": getattr(result.metadata, 'chunk_index', 0)
                },
                "similarity": result.similarity
            })

        return {
            "query": request.query,
            "response": response,
            "processing_time": processing_time,
            "context_used": len(results),
            "sources": sources,
            "total_sources": len(sources)
        }
        
    except Exception as e:
        logger.error(f"Error processing query: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error processing query: {str(e)}")
