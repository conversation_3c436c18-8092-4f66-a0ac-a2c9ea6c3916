import React, { useState } from 'react'
import {
  Box,
  Typography,
  Card,
  CardContent,
  TextField,
  Button,
  Paper,
  Chip,
  CircularProgress,
  Alert,
} from '@mui/material'
import {
  Search as SearchIcon,
  Send as SendIcon,
  History as HistoryIcon,
} from '@mui/icons-material'
import { motion } from 'framer-motion'
import { apiService } from '../../services/api'

interface QueryResult {
  query: string
  response: string
  processing_time: number
  context_used: number
  sources?: Array<{
    document_id: string
    content: string
    metadata: any
    similarity: number
  }>
  total_sources?: number
}

const QueryInterface: React.FC = () => {
  const [query, setQuery] = useState('')
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<QueryResult | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [loadingMessage, setLoadingMessage] = useState('')

  const sampleQueries = [
    "What are the latest treatments for diabetes?",
    "Summarize the COVID-19 research findings",
    "What are the side effects of cardiovascular medications?",
    "Find information about natural medicine approaches",
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!query.trim()) return

    try {
      setLoading(true)
      setError(null)
      setResult(null)
      setLoadingMessage('Initializing AI processing...')

      console.log('Submitting query:', query)

      // Simulate progress updates
      setTimeout(() => setLoadingMessage('Connecting to medical AI model...'), 1000)
      setTimeout(() => setLoadingMessage('Processing your medical query...'), 3000)
      setTimeout(() => setLoadingMessage('Generating evidence-based response...'), 8000)
      setTimeout(() => setLoadingMessage('Finalizing medical information...'), 15000)

      // Use apiService with correct format for real AI system
      const response = await apiService.post('/query', {
        query: query,
        user_context: { age: 35 } // Add default user context
      })
      console.log('Query response:', response)

      if (response) {
        setResult(response)
        console.log('Query result set:', response)
      } else {
        throw new Error('No data received from server')
      }
    } catch (err: any) {
      console.error('Error processing query:', err)
      console.error('Error details:', {
        message: err.message,
        response: err.response?.data,
        status: err.response?.status
      })

      let errorMessage = 'Failed to process query'

      if (err.code === 'ECONNABORTED' || err.message.includes('timeout')) {
        errorMessage = 'The AI model is taking longer than expected. This is normal for complex medical queries. Please try a shorter, more specific question.'
      } else {
        errorMessage = err.response?.data?.detail ||
                      err.response?.data?.message ||
                      err.message ||
                      'Failed to process query'
      }

      setError(errorMessage)
    } finally {
      setLoading(false)
      setLoadingMessage('')
    }
  }

  const handleSampleQuery = (sampleQuery: string) => {
    setQuery(sampleQuery)
  }

  return (
    <Box>
      {/* Page Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            AI Query Interface
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Ask questions about your documents using natural language
          </Typography>
        </Box>
      </motion.div>

      {/* Query Form */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <form onSubmit={handleSubmit}>
              <Box display="flex" gap={2} alignItems="flex-start">
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  placeholder="Ask a question about your documents..."
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  variant="outlined"
                  disabled={loading}
                />
                <Button
                  type="submit"
                  variant="contained"
                  size="large"
                  disabled={loading || !query.trim()}
                  startIcon={loading ? <CircularProgress size={20} /> : <SendIcon />}
                  sx={{ minWidth: 120, height: 'fit-content' }}
                >
                  {loading ? 'Processing...' : 'Ask'}
                </Button>
              </Box>

              {/* Loading Progress */}
              {loading && (
                <Box sx={{ mt: 2, textAlign: 'center' }}>
                  <CircularProgress sx={{ mb: 2 }} />
                  <Typography variant="body2" color="primary">
                    {loadingMessage || 'Processing your query...'}
                  </Typography>
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
                    Medical AI responses may take 30-60 seconds to generate
                  </Typography>
                </Box>
              )}
            </form>
          </CardContent>
        </Card>
      </motion.div>

      {/* Sample Queries */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Sample Queries
          </Typography>
          <Box display="flex" flexWrap="wrap" gap={1}>
            {sampleQueries.map((sampleQuery, index) => (
              <Chip
                key={index}
                label={sampleQuery}
                onClick={() => handleSampleQuery(sampleQuery)}
                variant="outlined"
                clickable
                disabled={loading}
              />
            ))}
          </Box>
        </Box>
      </motion.div>

      {/* Error Display */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Alert severity="error" sx={{ mb: 3 }}>
            <Typography variant="body1" gutterBottom>
              <strong>Error:</strong> {error}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Check the browser console (F12) for more details.
            </Typography>
          </Alert>
        </motion.div>
      )}

      {/* Result Display */}
      {result && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Paper sx={{ p: 3, mb: 3 }}>
            <Box display="flex" alignItems="center" mb={2}>
              <SearchIcon sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="h6">AI Response</Typography>
            </Box>

            <Typography variant="body1" gutterBottom>
              <strong>Query:</strong> {result.query}
            </Typography>

            <Typography variant="body1" gutterBottom sx={{ mt: 2, mb: 2 }}>
              <strong>Response:</strong>
            </Typography>

            <Paper sx={{ p: 2, bgcolor: 'grey.50', mb: 2 }}>
              <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                {result.response}
              </Typography>
            </Paper>

            <Box display="flex" gap={2} alignItems="center" mb={2}>
              <Chip
                label={`${result.context_used} context sources used`}
                color="info"
                size="small"
              />
              <Chip
                label={`${result.processing_time.toFixed(2)}s processing time`}
                color="success"
                size="small"
              />
              {result.sources && result.sources.length > 0 && (
                <Chip
                  label={`${result.sources.length} source documents`}
                  color="primary"
                  size="small"
                />
              )}
            </Box>

            {/* Sources Display */}
            {result.sources && result.sources.length > 0 && (
              <Box>
                <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                  Source Documents:
                </Typography>
                {result.sources.map((source, index) => (
                  <Paper key={index} sx={{ p: 2, mb: 1, bgcolor: 'grey.100' }}>
                    <Typography variant="caption" color="text.secondary">
                      Source {index + 1} • Similarity: {(source.similarity * 100).toFixed(1)}%
                    </Typography>
                    <Typography variant="body2" sx={{ mt: 0.5 }}>
                      {source.content}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, mt: 0.5, flexWrap: 'wrap' }}>
                      {source.metadata?.filename && (
                        <Typography variant="caption" color="primary">
                          📄 {source.metadata.filename}
                        </Typography>
                      )}
                      {source.metadata?.page && (
                        <Typography variant="caption" color="text.secondary">
                          📖 Page {source.metadata.page}
                        </Typography>
                      )}
                      {source.metadata?.chunk_id !== undefined && (
                        <Typography variant="caption" color="text.secondary">
                          🔗 Chunk {source.metadata.chunk_id}
                        </Typography>
                      )}
                    </Box>
                  </Paper>
                ))}
              </Box>
            )}
          </Paper>
        </motion.div>
      )}

      {/* Query History Placeholder */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.3 }}
      >
        <Card>
          <CardContent>
            <Box display="flex" alignItems="center" mb={2}>
              <HistoryIcon sx={{ mr: 1, color: 'text.secondary' }} />
              <Typography variant="h6">Recent Queries</Typography>
            </Box>
            <Typography variant="body2" color="text.secondary">
              Your recent queries will appear here
            </Typography>
          </CardContent>
        </Card>
      </motion.div>
    </Box>
  )
}

export default QueryInterface
