import React, { useEffect, useState } from 'react'
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Chip,
  IconButton,
  CircularProgress,
  Alert,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
} from '@mui/material'
import {
  Description as DocumentIcon,
  Download as DownloadIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material'
import { motion } from 'framer-motion'
import { apiService } from '../../services/api'
import DocumentPreview from '../../components/DocumentPreview/DocumentPreview'

interface Document {
  id: string
  title: string
  status: string
  created_at: string
  size: string
}

const DocumentLibrary: React.FC = () => {
  const [documents, setDocuments] = useState<Document[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [previewDocument, setPreviewDocument] = useState<Document | null>(null)
  const [deleteDocument, setDeleteDocument] = useState<Document | null>(null)

  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        setLoading(true)
        setError(null)
        const data = await apiService.get<Document[]>('/documents')
        setDocuments(data)
      } catch (err: any) {
        console.error('Error fetching documents:', err)
        setError(err.message || 'Failed to load documents')
      } finally {
        setLoading(false)
      }
    }

    fetchDocuments()
  }, [])

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'processed':
        return 'success'
      case 'processing':
        return 'warning'
      case 'error':
      case 'failed':
        return 'error'
      default:
        return 'default'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const handlePreview = (document: Document) => {
    setPreviewDocument(document)
  }

  const handleDownload = (document: Document) => {
    console.log('Downloading document:', document.title)
    // Implement download logic
  }

  const handleDelete = (document: Document) => {
    setDeleteDocument(document)
  }

  const confirmDelete = async () => {
    if (!deleteDocument) return

    try {
      // Implement delete API call
      console.log('Deleting document:', deleteDocument.title)
      setDocuments(prev => prev.filter(doc => doc.id !== deleteDocument.id))
      setDeleteDocument(null)
    } catch (err) {
      console.error('Error deleting document:', err)
    }
  }

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
      </Box>
    )
  }

  if (error) {
    return (
      <Box p={3}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button variant="contained" onClick={() => window.location.reload()}>
          Retry
        </Button>
      </Box>
    )
  }

  return (
    <Box>
      {/* Page Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Document Library
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your document collection - {documents.length} documents total
          </Typography>
        </Box>
      </motion.div>

      {/* Documents Grid */}
      <Box sx={{
        display: 'grid',
        gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', md: '1fr 1fr 1fr' },
        gap: 3
      }}>
        {documents.map((document, index) => (
          <motion.div
            key={document.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <CardContent sx={{ flexGrow: 1 }}>
                <Box display="flex" alignItems="flex-start" mb={2}>
                  <DocumentIcon sx={{ mr: 1, mt: 0.5, color: 'primary.main' }} />
                  <Box flexGrow={1}>
                    <Typography variant="h6" component="h3" gutterBottom>
                      {document.title}
                    </Typography>
                    <Chip
                      label={document.status}
                      color={getStatusColor(document.status) as any}
                      size="small"
                      sx={{ mb: 1 }}
                    />
                  </Box>
                </Box>

                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Created: {formatDate(document.created_at)}
                </Typography>

                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Size: {document.size}
                </Typography>

                <Box display="flex" justifyContent="flex-end" mt={2}>
                  <IconButton
                    size="small"
                    color="primary"
                    onClick={() => handlePreview(document)}
                    title="Preview document"
                  >
                    <ViewIcon />
                  </IconButton>
                  <IconButton
                    size="small"
                    color="primary"
                    onClick={() => handleDownload(document)}
                    title="Download document"
                  >
                    <DownloadIcon />
                  </IconButton>
                  <IconButton
                    size="small"
                    color="error"
                    onClick={() => handleDelete(document)}
                    title="Delete document"
                  >
                    <DeleteIcon />
                  </IconButton>
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </Box>

      {documents.length === 0 && (
        <Box textAlign="center" py={8}>
          <DocumentIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No documents found
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Upload your first document to get started
          </Typography>
        </Box>
      )}

      {/* Document Preview Dialog */}
      <DocumentPreview
        open={!!previewDocument}
        onClose={() => setPreviewDocument(null)}
        document={previewDocument}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={!!deleteDocument}
        onClose={() => setDeleteDocument(null)}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <DialogTitle id="delete-dialog-title">
          Delete Document
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="delete-dialog-description">
            Are you sure you want to delete "{deleteDocument?.title}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDocument(null)}>Cancel</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default DocumentLibrary
