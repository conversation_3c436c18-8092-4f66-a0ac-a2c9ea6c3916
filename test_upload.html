<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .progress {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .log {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #007bff; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Direct Upload Test</h1>
        <p>This page tests the upload functionality directly, bypassing any frontend framework issues.</p>
        
        <div class="form-group">
            <label for="filePath">File Path:</label>
            <input type="text" id="filePath" placeholder="C:\path\to\your\document.pdf" 
                   value="C:\Users\<USER>\August N\data\test_medical_research.txt">
        </div>
        
        <div class="form-group">
            <button onclick="testConnection()">Test Connection</button>
            <button onclick="uploadPDF()">Upload as PDF (OCR)</button>
            <button onclick="uploadDocument()">Upload as Document</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>
        
        <div id="progress" style="display: none;"></div>
        <div id="log" class="log"></div>
    </div>

    <script>
        let uploading = false;
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logDiv.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        async function testConnection() {
            log('🔍 Testing backend connection...', 'info');
            
            try {
                const response = await fetch('http://localhost:9876/health', {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Backend connection successful: ${response.status}`, 'success');
                    log(`Backend status: ${data.status}`, 'info');
                } else {
                    log(`❌ Backend connection failed: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                log(`❌ Connection error: ${error.message}`, 'error');
            }
        }
        
        async function uploadPDF() {
            await performUpload('/api/ocr/upload-file', true);
        }
        
        async function uploadDocument() {
            await performUpload('/api/documents/upload', false);
        }
        
        async function performUpload(endpoint, isPDF) {
            if (uploading) {
                log('⚠️ Upload already in progress', 'warning');
                return;
            }
            
            const filePath = document.getElementById('filePath').value.trim();
            if (!filePath) {
                log('⚠️ Please enter a file path', 'warning');
                return;
            }
            
            uploading = true;
            const progressDiv = document.getElementById('progress');
            progressDiv.style.display = 'block';
            
            log(`🚀 Starting ${isPDF ? 'PDF (OCR)' : 'document'} upload...`, 'info');
            log(`📁 File path: ${filePath}`, 'info');
            log(`🎯 Endpoint: ${endpoint}`, 'info');
            
            try {
                // Prepare payload
                const payload = isPDF ? {
                    file_path: filePath,
                    optimize_medical: true,
                    store_in_pinecone: true
                } : {
                    file_path: filePath
                };
                
                log(`📤 Sending upload request...`, 'info');
                log(`📋 Payload: ${JSON.stringify(payload, null, 2)}`, 'info');
                
                // Send upload request
                const uploadResponse = await fetch(`http://localhost:9876${endpoint}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payload)
                });
                
                if (!uploadResponse.ok) {
                    const errorText = await uploadResponse.text();
                    throw new Error(`Upload failed: ${uploadResponse.status} ${uploadResponse.statusText} - ${errorText}`);
                }
                
                const uploadResult = await uploadResponse.json();
                const documentId = uploadResult.document_id;
                
                log(`✅ Upload initiated successfully!`, 'success');
                log(`📄 Document ID: ${documentId}`, 'info');
                log(`📊 Status: ${uploadResult.status}`, 'info');
                
                // Track progress
                const progressEndpoint = isPDF ? `/api/ocr/progress/${documentId}` : `/api/documents/upload/progress/${documentId}`;
                log(`📊 Starting progress tracking: ${progressEndpoint}`, 'info');
                
                for (let i = 0; i < 30; i++) {
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    
                    try {
                        const progressResponse = await fetch(`http://localhost:9876${progressEndpoint}`, {
                            method: 'GET',
                            headers: { 'Content-Type': 'application/json' }
                        });
                        
                        if (progressResponse.ok) {
                            const progressData = await progressResponse.json();
                            
                            const progress = progressData.progress || 0;
                            const status = progressData.status || 'processing';
                            const message = progressData.step || progressData.message || 'Processing...';
                            const vectors = progressData.vectors_created || 0;
                            const pages = progressData.pages || 0;
                            
                            progressDiv.innerHTML = `
                                <strong>Progress: ${progress}%</strong><br>
                                Status: ${status}<br>
                                Message: ${message}<br>
                                ${vectors > 0 ? `Vectors Created: ${vectors}<br>` : ''}
                                ${pages > 0 ? `Pages Processed: ${pages}<br>` : ''}
                            `;
                            
                            log(`📈 Progress: ${progress}% - ${message}`, 'info');
                            if (vectors > 0) log(`✅ Vectors created: ${vectors}`, 'success');
                            if (pages > 0) log(`📄 Pages processed: ${pages}`, 'info');
                            
                            if (status === 'completed' || progress === 100) {
                                log(`🎉 Upload completed successfully!`, 'success');
                                break;
                            }
                            
                            if (status === 'error') {
                                throw new Error(`Processing failed: ${message}`);
                            }
                        } else {
                            log(`⚠️ Progress check failed: ${progressResponse.status}`, 'warning');
                        }
                    } catch (progressError) {
                        log(`⚠️ Progress polling error: ${progressError.message}`, 'warning');
                    }
                }
                
            } catch (error) {
                log(`❌ Upload failed: ${error.message}`, 'error');
                progressDiv.innerHTML = `<strong style="color: red;">Error: ${error.message}</strong>`;
            } finally {
                uploading = false;
                setTimeout(() => {
                    progressDiv.style.display = 'none';
                }, 5000);
            }
        }
        
        // Test connection on page load
        window.addEventListener('load', () => {
            log('🌐 Page loaded, testing connection...', 'info');
            testConnection();
        });
    </script>
</body>
</html>
