import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { VitePWA } from 'vite-plugin-pwa'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    VitePWA({
      registerType: 'autoUpdate',
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg}']
      },
      includeAssets: ['favicon.ico', 'apple-touch-icon.png', 'masked-icon.svg'],
      manifest: {
        name: 'August N - Natural Medicine AI',
        short_name: 'August N',
        description: 'Multiagent Agentic Natural Medicine AI Framework',
        theme_color: '#2e7d32',
        background_color: '#ffffff',
        display: 'standalone',
        icons: [
          {
            src: 'pwa-192x192.png',
            sizes: '192x192',
            type: 'image/png'
          },
          {
            src: 'pwa-512x512.png',
            sizes: '512x512',
            type: 'image/png'
          }
        ]
      }
    })
  ],
  server: {
    port: 3001,
    proxy: {
      '/api': {
        target: 'http://localhost:9876',  // Real backend with Pinecone integration
        changeOrigin: true,
        secure: false,
        timeout: 180000, // 3 minutes for AI processing
        rewrite: (path) => path.replace(/^\/api/, '/api')  // Keep /api prefix
      },
      '/ws': {
        target: 'ws://localhost:9876',
        ws: true,
        changeOrigin: true
      },
      '/health': {
        target: 'http://localhost:9876',
        changeOrigin: true,
        secure: false
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['@mui/material', '@mui/icons-material'],
          charts: ['recharts'],
          utils: ['lodash', 'date-fns', 'uuid']
        }
      }
    }
  }
})
