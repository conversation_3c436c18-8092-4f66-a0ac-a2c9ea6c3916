import React, { useEffect, useState } from 'react'
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,

  Switch,
  FormControlLabel,
  TextField,
  Button,
  Divider,
  Alert,
  CircularProgress,
  Chip,
} from '@mui/material'
import {
  Save as SaveIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material'
import { motion } from 'framer-motion'
import { apiService } from '../../services/api'
import LLMSelector from '../../components/LLMSelector/LLMSelector'

interface SystemSettings {
  aiModel: string
  maxResponseLength: number
  temperature: number
  enableLogging: boolean
  autoRefresh: boolean
  theme: string
}

const Settings: React.FC = () => {
  const [settings, setSettings] = useState<SystemSettings>({
    aiModel: 'granite3.1-moe:latest',
    maxResponseLength: 800,
    temperature: 0.7,
    enableLogging: true,
    autoRefresh: true,
    theme: 'light',
  })
  const [systemStatus, setSystemStatus] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)

  useEffect(() => {
    const fetchSystemStatus = async () => {
      try {
        setLoading(true)
        const data = await apiService.get('/system/status')
        setSystemStatus(data)
      } catch (err: any) {
        console.error('Error fetching system status:', err)
        setMessage({ type: 'error', text: 'Failed to load system status' })
      } finally {
        setLoading(false)
      }
    }

    fetchSystemStatus()
  }, [])

  const handleSave = async () => {
    setSaving(true)
    setMessage(null)

    try {
      // Simulate saving settings
      await new Promise(resolve => setTimeout(resolve, 1000))
      setMessage({ type: 'success', text: 'Settings saved successfully!' })
    } catch (err: any) {
      setMessage({ type: 'error', text: 'Failed to save settings' })
    } finally {
      setSaving(false)
    }
  }

  const handleRefresh = async () => {
    setLoading(true)
    setMessage(null)

    try {
      const data = await apiService.get('/system/status')
      setSystemStatus(data)
      setMessage({ type: 'success', text: 'System status refreshed!' })
    } catch (err: any) {
      setMessage({ type: 'error', text: 'Failed to refresh system status' })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Box>
      {/* Page Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            System Settings
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Configure AI models, processing parameters, and system preferences
          </Typography>
        </Box>
      </motion.div>

      {/* Messages */}
      {message && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Alert severity={message.type} sx={{ mb: 3 }}>
            {message.text}
          </Alert>
        </motion.div>
      )}

      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
        {/* LLM Provider Configuration */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <LLMSelector showDetails />
        </motion.div>

        {/* Configuration Cards */}
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 3 }}>
          {/* AI Configuration */}
          <Box sx={{ flex: 1 }}>
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    AI Model Configuration
                  </Typography>

                  <TextField
                    fullWidth
                    label="AI Model"
                    value={settings.aiModel}
                    onChange={(e) => setSettings({ ...settings, aiModel: e.target.value })}
                    sx={{ mb: 2 }}
                    helperText="Current AI model being used for queries"
                  />

                  <TextField
                    fullWidth
                    label="Max Response Length"
                    type="number"
                    value={settings.maxResponseLength}
                    onChange={(e) => setSettings({ ...settings, maxResponseLength: parseInt(e.target.value) })}
                    sx={{ mb: 2 }}
                    helperText="Maximum number of tokens in AI responses"
                  />

                  <TextField
                    fullWidth
                    label="Temperature"
                    type="number"
                    inputProps={{ min: 0, max: 1, step: 0.1 }}
                    value={settings.temperature}
                    onChange={(e) => setSettings({ ...settings, temperature: parseFloat(e.target.value) })}
                    sx={{ mb: 2 }}
                    helperText="Controls randomness in AI responses (0.0 - 1.0)"
                  />
                </CardContent>
              </Card>
            </motion.div>
          </Box>

          {/* System Preferences */}
          <Box sx={{ flex: 1 }}>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: 0.3 }}
            >
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    System Preferences
                  </Typography>

                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.enableLogging}
                        onChange={(e) => setSettings({ ...settings, enableLogging: e.target.checked })}
                      />
                    }
                    label="Enable System Logging"
                    sx={{ mb: 2, display: 'block' }}
                  />

                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.autoRefresh}
                        onChange={(e) => setSettings({ ...settings, autoRefresh: e.target.checked })}
                      />
                    }
                    label="Auto-refresh Dashboard"
                    sx={{ mb: 2, display: 'block' }}
                  />

                  <Divider sx={{ my: 2 }} />

                  <Box display="flex" gap={2}>
                    <Button
                      variant="contained"
                      startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
                      onClick={handleSave}
                      disabled={saving}
                    >
                      {saving ? 'Saving...' : 'Save Settings'}
                    </Button>

                    <Button
                      variant="outlined"
                      startIcon={<RefreshIcon />}
                      onClick={handleRefresh}
                      disabled={loading}
                    >
                      Refresh Status
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Box>
        </Box>

        {/* System Status */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                System Status
              </Typography>

              {loading ? (
                <Box display="flex" justifyContent="center" py={4}>
                  <CircularProgress />
                </Box>
              ) : systemStatus ? (
                <Box sx={{
                  display: 'grid',
                  gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', md: '1fr 1fr 1fr 1fr' },
                  gap: 2
                }}>
                  <Box textAlign="center">
                    <Typography variant="body2" color="text.secondary">
                      Overall Status
                    </Typography>
                    <Chip
                      label={systemStatus.overall || systemStatus.status}
                      color={(systemStatus.overall || systemStatus.status) === 'healthy' ? 'success' : 'error'}
                      sx={{ mt: 1 }}
                    />
                  </Box>

                  {systemStatus.services && systemStatus.services.map((service: any, index: number) => (
                    <Box textAlign="center" key={index}>
                      <Typography variant="body2" color="text.secondary">
                        {service.name}
                      </Typography>
                      <Chip
                        label={service.status}
                        color={service.status === 'healthy' ? 'success' : service.status === 'warning' ? 'warning' : 'error'}
                        size="small"
                        sx={{ mt: 1 }}
                      />
                    </Box>
                  ))}
                </Box>
              ) : (
                <Typography color="text.secondary">
                  No system status available
                </Typography>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </Box>
    </Box>
  )
}

export default Settings
