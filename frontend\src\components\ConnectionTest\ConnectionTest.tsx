import React, { useState, useEffect } from 'react'
import { 
  <PERSON>, 
  <PERSON>, 
  CardContent, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>, 
  <PERSON>,
  Stack,
  Alert
} from '@mui/material'
import { 
  Wifi, 
  WifiOff, 
  Refresh,
  CheckCircle,
  Error as ErrorIcon
} from '@mui/icons-material'

interface ConnectionStatus {
  backend: boolean
  proxy: boolean
  lastChecked: string
  backendResponse?: any
  proxyResponse?: any
  error?: string
}

const ConnectionTest: React.FC = () => {
  const [status, setStatus] = useState<ConnectionStatus>({
    backend: false,
    proxy: false,
    lastChecked: 'Never'
  })
  const [testing, setTesting] = useState(false)

  const testConnections = async () => {
    setTesting(true)
    const newStatus: ConnectionStatus = {
      backend: false,
      proxy: false,
      lastChecked: new Date().toLocaleTimeString()
    }

    try {
      // Test direct backend connection
      console.log('🔍 Testing direct backend connection...')
      const backendResponse = await fetch('http://localhost:9876/health', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      })

      if (backendResponse.ok) {
        newStatus.backend = true
        newStatus.backendResponse = await backendResponse.json()
        console.log('✅ Direct backend connection successful')
      } else {
        console.log('❌ Direct backend connection failed:', backendResponse.status)
      }
    } catch (error) {
      console.log('❌ Direct backend connection error:', error)
      newStatus.error = `Backend: ${error instanceof Error ? error.message : 'Unknown error'}`
    }

    try {
      // Test proxy connection
      console.log('🔍 Testing proxy connection...')
      const proxyResponse = await fetch('/api/health', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      })

      if (proxyResponse.ok) {
        newStatus.proxy = true
        newStatus.proxyResponse = await proxyResponse.json()
        console.log('✅ Proxy connection successful')
      } else {
        console.log('❌ Proxy connection failed:', proxyResponse.status)
      }
    } catch (error) {
      console.log('❌ Proxy connection error:', error)
      if (!newStatus.error) {
        newStatus.error = `Proxy: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }

    setStatus(newStatus)
    setTesting(false)
  }

  useEffect(() => {
    testConnections()
  }, [])

  return (
    <Card sx={{ mb: 2 }}>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Box display="flex" alignItems="center" gap={1}>
            {status.backend || status.proxy ? (
              <Wifi color="success" />
            ) : (
              <WifiOff color="error" />
            )}
            <Typography variant="h6">Connection Status</Typography>
          </Box>
          
          <Button
            variant="outlined"
            size="small"
            startIcon={<Refresh />}
            onClick={testConnections}
            disabled={testing}
          >
            {testing ? 'Testing...' : 'Test'}
          </Button>
        </Box>

        <Stack spacing={2}>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Typography variant="body2">Direct Backend (Port 9876):</Typography>
            <Chip
              icon={status.backend ? <CheckCircle /> : <ErrorIcon />}
              label={status.backend ? 'Connected' : 'Failed'}
              color={status.backend ? 'success' : 'error'}
              size="small"
            />
          </Box>

          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Typography variant="body2">Proxy Connection (Port 3887):</Typography>
            <Chip
              icon={status.proxy ? <CheckCircle /> : <ErrorIcon />}
              label={status.proxy ? 'Connected' : 'Failed'}
              color={status.proxy ? 'success' : 'error'}
              size="small"
            />
          </Box>

          <Typography variant="caption" color="textSecondary">
            Last checked: {status.lastChecked}
          </Typography>

          {status.error && (
            <Alert severity="error" sx={{ fontSize: '0.8rem' }}>
              {status.error}
            </Alert>
          )}

          {status.backend && (
            <Alert severity="success" sx={{ fontSize: '0.8rem' }}>
              ✅ Backend is accessible! Upload functionality should work.
            </Alert>
          )}

          {!status.backend && !status.proxy && (
            <Alert severity="error" sx={{ fontSize: '0.8rem' }}>
              ❌ No connection to backend. Check if the backend is running on port 9876.
            </Alert>
          )}

          {status.proxy && !status.backend && (
            <Alert severity="warning" sx={{ fontSize: '0.8rem' }}>
              ⚠️ Proxy works but direct connection fails. This might cause issues.
            </Alert>
          )}
        </Stack>
      </CardContent>
    </Card>
  )
}

export default ConnectionTest
