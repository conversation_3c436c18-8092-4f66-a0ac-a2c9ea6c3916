import React from 'react'
import {
  <PERSON>,
  CardContent,
  Typography,
  Box,
  LinearProgress,
  Chip,
  useTheme,
} from '@mui/material'
import {
  CheckCircle as HealthyIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Memory as MemoryIcon,
  Storage as StorageIcon,
  Speed as SpeedIcon,
  NetworkCheck as NetworkIcon,
} from '@mui/icons-material'
import { motion } from 'framer-motion'

interface SystemStatusProps {
  status: {
    status: string
    uptime: string
    components: {
      [key: string]: {
        status: string
        [key: string]: any
      }
    }
    performance: {
      [key: string]: any
    }
  }
}

const SystemStatus: React.FC<SystemStatusProps> = ({ status }) => {
  const theme = useTheme()

  const getStatusIcon = (statusType: string) => {
    switch (statusType.toLowerCase()) {
      case 'operational':
      case 'healthy':
        return <HealthyIcon sx={{ color: theme.palette.success.main }} />
      case 'warning':
      case 'fallback_mode':
        return <WarningIcon sx={{ color: theme.palette.warning.main }} />
      case 'error':
      case 'failed':
        return <ErrorIcon sx={{ color: theme.palette.error.main }} />
      default:
        return <HealthyIcon sx={{ color: theme.palette.success.main }} />
    }
  }

  const getStatusColor = (statusType: string): 'success' | 'warning' | 'error' => {
    switch (statusType.toLowerCase()) {
      case 'operational':
      case 'healthy':
        return 'success'
      case 'warning':
      case 'fallback_mode':
        return 'warning'
      case 'error':
      case 'failed':
        return 'error'
      default:
        return 'success'
    }
  }

  const getMetricColor = (value: number) => {
    if (value < 50) return theme.palette.success.main
    if (value < 80) return theme.palette.warning.main
    return theme.palette.error.main
  }

  // Convert components to services array
  const services = Object.entries(status.components).map(([name, component]) => ({
    name: name.charAt(0).toUpperCase() + name.slice(1).replace('_', ' '),
    status: component.status,
    uptime: status.uptime
  }))

  // Create performance metrics from our API data
  const performanceMetrics = [
    {
      label: 'Total Documents',
      value: status.performance.total_documents || 0,
      icon: StorageIcon,
      unit: '',
      displayValue: status.performance.total_documents?.toString() || '0'
    },
    {
      label: 'Query Time',
      value: 75, // Mock percentage for progress bar
      icon: SpeedIcon,
      unit: '',
      displayValue: status.performance.avg_query_time || 'N/A'
    },
    {
      label: 'AI Response Time',
      value: 60, // Mock percentage for progress bar
      icon: NetworkIcon,
      unit: '',
      displayValue: status.performance.ai_response_time || 'N/A'
    },
    {
      label: 'Embedding Mode',
      value: status.performance.embedding_mode === 'mock_fallback' ? 50 : 90,
      icon: MemoryIcon,
      unit: '',
      displayValue: status.performance.embedding_mode?.replace('_', ' ') || 'N/A'
    },
  ]

  return (
    <Card>
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            System Status
          </Typography>
          <Chip
            icon={getStatusIcon(status.status)}
            label={status.status.toUpperCase()}
            color={getStatusColor(status.status)}
            variant="outlined"
          />
        </Box>

        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 3 }}>
          {/* Services Status */}
          <Box sx={{ flex: 1 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
              Services
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              {services.map((service, index) => (
                <motion.div
                  key={service.name}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.2, delay: index * 0.05 }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      p: 1.5,
                      borderRadius: 1,
                      backgroundColor: theme.palette.background.default,
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {getStatusIcon(service.status)}
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {service.name}
                      </Typography>
                    </Box>
                    <Typography variant="caption" color="text.secondary">
                      {service.uptime} uptime
                    </Typography>
                  </Box>
                </motion.div>
              ))}
            </Box>
          </Box>

          {/* System Metrics */}
          <Box sx={{ flex: 1 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
              Performance Metrics
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {performanceMetrics.map((metric, index) => {
                const Icon = metric.icon
                return (
                  <motion.div
                    key={metric.label}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.2, delay: index * 0.05 }}
                  >
                    <Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Icon sx={{ fontSize: 16, color: theme.palette.text.secondary }} />
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {metric.label}
                          </Typography>
                        </Box>
                        <Typography
                          variant="body2"
                          sx={{ color: getMetricColor(metric.value), fontWeight: 600 }}
                        >
                          {metric.displayValue}{metric.unit}
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={metric.value}
                        sx={{
                          height: 6,
                          borderRadius: 3,
                          backgroundColor: theme.palette.grey[200],
                          '& .MuiLinearProgress-bar': {
                            backgroundColor: getMetricColor(metric.value),
                            borderRadius: 3,
                          },
                        }}
                      />
                    </Box>
                  </motion.div>
                )
              })}
            </Box>
          </Box>
        </Box>
      </CardContent>
    </Card>
  )
}

export default SystemStatus
