import React from 'react'
import {
  <PERSON>,
  CardContent,
  Typography,
  Box,
  LinearProgress,
  Chip,
  useTheme,
} from '@mui/material'
import {
  CheckCircle as HealthyIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Memory as MemoryIcon,
  Storage as StorageIcon,
  Speed as SpeedIcon,
  NetworkCheck as NetworkIcon,
} from '@mui/icons-material'
import { motion } from 'framer-motion'

interface SystemStatusProps {
  status: {
    overall: string
    services: Array<{
      name: string
      status: string
      uptime: string
    }>
    performance?: {
      [key: string]: any
    }
  }
}

const SystemStatus: React.FC<SystemStatusProps> = ({ status }) => {
  const theme = useTheme()

  // Handle null or undefined status
  if (!status) {
    return (
      <Card>
        <CardContent sx={{ p: 3 }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            System Status
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            Loading system status...
          </Typography>
        </CardContent>
      </Card>
    )
  }

  const getStatusIcon = (statusType: string) => {
    switch (statusType.toLowerCase()) {
      case 'operational':
      case 'healthy':
        return <HealthyIcon sx={{ color: theme.palette.success.main }} />
      case 'warning':
      case 'fallback_mode':
        return <WarningIcon sx={{ color: theme.palette.warning.main }} />
      case 'error':
      case 'failed':
        return <ErrorIcon sx={{ color: theme.palette.error.main }} />
      default:
        return <HealthyIcon sx={{ color: theme.palette.success.main }} />
    }
  }

  const getStatusColor = (statusType: string): 'success' | 'warning' | 'error' => {
    switch (statusType.toLowerCase()) {
      case 'operational':
      case 'healthy':
        return 'success'
      case 'warning':
      case 'fallback_mode':
        return 'warning'
      case 'error':
      case 'failed':
        return 'error'
      default:
        return 'success'
    }
  }

  const getMetricColor = (value: number) => {
    if (value < 50) return theme.palette.success.main
    if (value < 80) return theme.palette.warning.main
    return theme.palette.error.main
  }

  // Use services array directly from API
  const services = status.services || []

  // Create performance metrics from our API data
  const performanceMetrics = [
    {
      label: 'Total Services',
      value: services.length,
      icon: StorageIcon,
      unit: '',
      displayValue: services.length.toString()
    },
    {
      label: 'Healthy Services',
      value: services.filter(s => s.status === 'healthy').length,
      icon: SpeedIcon,
      unit: '',
      displayValue: services.filter(s => s.status === 'healthy').length.toString()
    },
    {
      label: 'System Health',
      value: status.overall === 'healthy' ? 100 : status.overall === 'warning' ? 75 : 25,
      icon: NetworkIcon,
      unit: '%',
      displayValue: status.overall.toUpperCase()
    },
    {
      label: 'Average Uptime',
      value: 95, // Mock percentage for progress bar
      icon: MemoryIcon,
      unit: '%',
      displayValue: '99.5%'
    },
  ]

  return (
    <Card>
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            System Status
          </Typography>
          <Chip
            icon={getStatusIcon(status.overall)}
            label={status.overall.toUpperCase()}
            color={getStatusColor(status.overall)}
            variant="outlined"
          />
        </Box>

        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 3 }}>
          {/* Services Status */}
          <Box sx={{ flex: 1 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
              Services
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              {services.map((service, index) => (
                <motion.div
                  key={service.name}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.2, delay: index * 0.05 }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      p: 1.5,
                      borderRadius: 1,
                      backgroundColor: theme.palette.background.default,
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {getStatusIcon(service.status)}
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {service.name}
                      </Typography>
                    </Box>
                    <Typography variant="caption" color="text.secondary">
                      {service.uptime}
                    </Typography>
                  </Box>
                </motion.div>
              ))}
            </Box>
          </Box>

          {/* System Metrics */}
          <Box sx={{ flex: 1 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
              Performance Metrics
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {performanceMetrics.map((metric, index) => {
                const Icon = metric.icon
                return (
                  <motion.div
                    key={metric.label}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.2, delay: index * 0.05 }}
                  >
                    <Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Icon sx={{ fontSize: 16, color: theme.palette.text.secondary }} />
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {metric.label}
                          </Typography>
                        </Box>
                        <Typography
                          variant="body2"
                          sx={{ color: getMetricColor(metric.value), fontWeight: 600 }}
                        >
                          {metric.displayValue}{metric.unit}
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={metric.value}
                        sx={{
                          height: 6,
                          borderRadius: 3,
                          backgroundColor: theme.palette.grey[200],
                          '& .MuiLinearProgress-bar': {
                            backgroundColor: getMetricColor(metric.value),
                            borderRadius: 3,
                          },
                        }}
                      />
                    </Box>
                  </motion.div>
                )
              })}
            </Box>
          </Box>
        </Box>
      </CardContent>
    </Card>
  )
}

export default SystemStatus
