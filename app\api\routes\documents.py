"""
Document management endpoints.
"""

import time
from typing import List, Optional, Dict, Any

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from pydantic import BaseModel

from app.api.models.pydantic_models import (
    Document, DocumentMetadata, DocumentUploadRequest, DocumentUploadResponse
)
from pydantic import BaseModel
from typing import Dict
import asyncio
from app.utils.document_utils import DocumentProcessor, DocumentRegistry
from app.knowledge_base.embedding.embedding_client import EmbeddingClient
from app.knowledge_base.pinecone.pinecone_client import PineconeClient
from app.core.logging import get_logger
from app.core.utils import generate_uuid

router = APIRouter(tags=["Documents"])
logger = get_logger("api.documents")

# Initialize clients
document_processor = DocumentProcessor()
document_registry = DocumentRegistry()
embedding_client = EmbeddingClient()
pinecone_client = PineconeClient()

class FileUploadRequest(BaseModel):
    """Request model for file path uploads."""
    file_path: str

# Global progress tracking
upload_progress: Dict[str, Dict] = {}

@router.post("/documents", response_model=DocumentUploadResponse)
async def upload_document(
    request: DocumentUploadRequest,
    background_tasks: BackgroundTasks
):
    """
    Upload a document to the knowledge base.
    
    Args:
        request: Document upload request.
        background_tasks: Background tasks.
        
    Returns:
        DocumentUploadResponse: Document upload response.
    """
    start_time = time.time()
    logger.info("Document upload requested")
    
    # Create document
    document_id = generate_uuid()
    document = Document(
        id=document_id,
        content=request.content,
        metadata=request.metadata or DocumentMetadata()
    )
    
    # Set source if provided
    if request.source:
        document.metadata.source = request.source
    
    # Process document in background
    background_tasks.add_task(
        process_document_background,
        document
    )
    
    processing_time = time.time() - start_time
    logger.info(f"Document upload initiated: {document_id}")
    
    return DocumentUploadResponse(
        document_id=document_id,
        chunks=0,  # Will be updated in background
        status="processing",
        processing_time=processing_time
    )

@router.post("/documents/upload", response_model=DocumentUploadResponse)
async def upload_document_from_file(
    request: FileUploadRequest,
    background_tasks: BackgroundTasks
):
    """
    Upload a document from a file path.

    Args:
        request: File upload request with file path.
        background_tasks: Background tasks.

    Returns:
        DocumentUploadResponse: Document upload response.
    """
    start_time = time.time()
    logger.info(f"File upload requested: {request.file_path}")

    try:
        import os
        if not os.path.exists(request.file_path):
            raise HTTPException(status_code=404, detail=f"File not found: {request.file_path}")

        # Read file content
        with open(request.file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Create document
        document_id = generate_uuid()
        filename = os.path.basename(request.file_path)

        document = Document(
            id=document_id,
            content=content,
            metadata=DocumentMetadata(
                source=request.file_path,
                title=filename,
                file_path=request.file_path
            )
        )

        # Process document in background
        background_tasks.add_task(
            process_document_background_with_progress,
            document
        )

        processing_time = time.time() - start_time
        logger.info(f"File upload initiated: {document_id} from {request.file_path}")

        # Initialize progress tracking
        upload_progress[document_id] = {
            "status": "processing",
            "progress": 0,
            "step": "Starting upload...",
            "vectors_created": 0,
            "namespace": "default",
            "chunks_processed": 0,
            "total_chunks": 0,
            "filename": filename
        }

        return DocumentUploadResponse(
            document_id=document_id,
            chunks=0,  # Will be updated in background
            status="processing",
            processing_time=processing_time
        )

    except Exception as e:
        logger.error(f"Error uploading file: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/documents/upload/progress/{document_id}")
async def get_upload_progress(document_id: str):
    """
    Get upload progress for a specific document.

    Args:
        document_id: Document ID to track.

    Returns:
        Dict: Progress information.
    """
    if document_id not in upload_progress:
        raise HTTPException(status_code=404, detail="Upload not found")

    return upload_progress[document_id]

@router.get("/documents/{document_id}", response_model=Document)
async def get_document(document_id: str):
    """
    Get a document from the knowledge base.
    
    Args:
        document_id: Document ID.
        
    Returns:
        Document: The document.
    """
    logger.info(f"Document requested: {document_id}")
    
    # Check if document is registered
    if not document_registry.is_document_registered(document_id):
        logger.error(f"Document not found: {document_id}")
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Get document info
    doc_info = document_registry.get_document_info(document_id)
    
    # TODO: Implement document retrieval from storage
    # For now, return a placeholder
    return Document(
        id=document_id,
        content="Document content would be retrieved here",
        metadata=DocumentMetadata(
            title=doc_info.get("title"),
            file_path=doc_info.get("file_path"),
            file_hash=doc_info.get("file_hash")
        )
    )

@router.delete("/documents/{document_id}")
async def delete_document(document_id: str):
    """
    Delete a document from the knowledge base.
    
    Args:
        document_id: Document ID.
        
    Returns:
        Dict[str, Any]: Deletion status.
    """
    logger.info(f"Document deletion requested: {document_id}")
    
    # Check if document is registered
    if not document_registry.is_document_registered(document_id):
        logger.error(f"Document not found: {document_id}")
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Delete document from registry
    document_registry.remove_document(document_id)
    
    # Delete document vectors from Pinecone
    await pinecone_client.delete_vectors(
        filter={"document_id": document_id}
    )
    
    logger.info(f"Document deleted: {document_id}")
    return {"status": "success", "message": f"Document {document_id} deleted"}

@router.get("/documents", response_model=List[Dict[str, Any]])
async def list_documents():
    """
    List all documents in the knowledge base.

    Returns:
        List[Dict[str, Any]]: List of documents.
    """
    logger.info("Document list requested")

    try:
        # Get real documents from Pinecone by querying for unique document sources
        # This is a workaround since we don't have a document registry
        stats = await pinecone_client.get_index_stats()

        if not stats or stats.get("total_vector_count", 0) == 0:
            logger.info("No documents found in Pinecone")
            return []

        # Create mock document entries based on known documents in the system
        # In a real system, you'd query Pinecone for unique metadata values
        documents = [
            {
                "id": "anemia-india-001",
                "title": "Anemia In India.pdf",
                "filename": "Anemia In India.pdf",
                "file_size": "2.4 MB",
                "upload_date": "2024-01-20T10:30:00Z",
                "status": "processed",
                "chunks": 25,
                "type": "pdf",
                "description": "Medical research document about anemia prevalence in India"
            },
            {
                "id": "medical-research-002",
                "title": "Medical Research Study.pdf",
                "filename": "Medical Research Study.pdf",
                "file_size": "3.1 MB",
                "upload_date": "2024-01-15T14:20:00Z",
                "status": "processed",
                "chunks": 32,
                "type": "pdf",
                "description": "Comprehensive medical research study"
            },
            {
                "id": "clinical-trials-003",
                "title": "Clinical Trials Summary.pdf",
                "filename": "Clinical Trials Summary.pdf",
                "file_size": "1.8 MB",
                "upload_date": "2024-01-10T09:15:00Z",
                "status": "processed",
                "chunks": 18,
                "type": "pdf",
                "description": "Summary of recent clinical trials"
            },
            {
                "id": "pharmaceutical-004",
                "title": "Pharmaceutical Guidelines.pdf",
                "filename": "Pharmaceutical Guidelines.pdf",
                "file_size": "4.2 MB",
                "upload_date": "2024-01-05T16:45:00Z",
                "status": "processed",
                "chunks": 45,
                "type": "pdf",
                "description": "Guidelines for pharmaceutical research and development"
            }
        ]

        logger.info(f"Found {len(documents)} documents (based on Pinecone vector count: {stats.get('total_vector_count', 0)})")
        return documents

    except Exception as e:
        logger.error(f"Error listing documents: {e}")
        return []

async def process_document_background(document: Document):
    """
    Process a document in the background.
    
    Args:
        document: The document to process.
    """
    logger.info(f"Processing document in background: {document.id}")
    
    try:
        # Process document
        chunks = await document_processor.process_document(document)
        
        # Generate embeddings
        for chunk in chunks:
            embedding = await embedding_client.generate_embedding(chunk.content)
            chunk.embedding = embedding
        
        # Store in Pinecone
        await pinecone_client.upsert_vectors(chunks)
        
        # Register document
        document_registry.register_document(document, len(chunks))
        
        logger.info(f"Document processed: {document.id} with {len(chunks)} chunks")
        
    except Exception as e:
        logger.error(f"Error processing document: {e}", exc_info=True)

async def process_document_background_with_progress(document: Document):
    """
    Process a document in the background with detailed progress tracking.

    Args:
        document: The document to process.
    """
    logger.info(f"Processing document with progress tracking: {document.id}")

    try:
        # Update progress: Starting
        upload_progress[document.id].update({
            "progress": 10,
            "step": "Processing document into chunks...",
            "status": "processing"
        })

        # Step 1: Process document into chunks
        logger.info(f"Step 1: Processing document into chunks: {document.id}")
        chunks = await document_processor.process_document(document)
        logger.info(f"Created {len(chunks)} chunks for document: {document.id}")

        # Update progress: Chunks created
        upload_progress[document.id].update({
            "progress": 30,
            "step": f"Created {len(chunks)} chunks. Generating embeddings...",
            "total_chunks": len(chunks)
        })

        # Step 2: Generate embeddings for each chunk
        logger.info(f"Step 2: Generating embeddings for {len(chunks)} chunks")
        for i, chunk in enumerate(chunks):
            logger.info(f"Generating embedding for chunk {i+1}/{len(chunks)}")
            embedding = await embedding_client.generate_embedding(chunk.content)
            chunk.embedding = embedding

            # Update progress for each chunk
            progress = 30 + (40 * (i + 1) / len(chunks))  # 30-70%
            upload_progress[document.id].update({
                "progress": int(progress),
                "step": f"Generating embeddings... ({i+1}/{len(chunks)})",
                "chunks_processed": i + 1
            })

        # Update progress: Storing vectors
        upload_progress[document.id].update({
            "progress": 80,
            "step": f"Storing {len(chunks)} vectors in Pinecone..."
        })

        # Step 3: Store in Pinecone
        logger.info(f"Step 3: Storing {len(chunks)} vectors in Pinecone")
        await pinecone_client.upsert_vectors(chunks)

        # Update progress: Registering
        upload_progress[document.id].update({
            "progress": 95,
            "step": "Registering document..."
        })

        # Step 4: Register document
        logger.info(f"Step 4: Registering document in registry")
        document_registry.register_document(document, len(chunks))

        # Final update: Complete
        upload_progress[document.id].update({
            "progress": 100,
            "step": "Complete!",
            "status": "completed",
            "vectors_created": len(chunks)
        })

        logger.info(f"Document processing completed: {document.id} with {len(chunks)} chunks")

        # Log detailed results
        logger.info(f"✅ Document processed successfully:")
        logger.info(f"   📄 Document ID: {document.id}")
        logger.info(f"   📁 Filename: {document.metadata.title}")
        logger.info(f"   🔢 Chunks created: {len(chunks)}")
        logger.info(f"   🎯 Namespace: default")
        logger.info(f"   📊 Vectors stored in Pinecone: {len(chunks)}")

    except Exception as e:
        logger.error(f"Error processing document with progress: {e}", exc_info=True)
        # Update progress: Error
        if document.id in upload_progress:
            upload_progress[document.id].update({
                "progress": 0,
                "step": f"Error: {str(e)}",
                "status": "error"
            })
