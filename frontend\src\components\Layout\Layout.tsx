import React from 'react'
import { Box, useTheme, useMediaQuery } from '@mui/material'

import Sidebar from './Sidebar'
import Header from './Header'
import { useAppSelector } from '../../store'

interface LayoutProps {
  children: React.ReactNode
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  const sidebarOpen = useAppSelector((state) => state.ui.sidebarOpen)

  const sidebarWidth = 280
  const collapsedSidebarWidth = 64

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      {/* Sidebar */}
      <Sidebar />

      {/* Main content area */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
          marginLeft: isMobile ? 0 : sidebarOpen ? `${sidebarWidth}px` : `${collapsedSidebarWidth}px`,
          minHeight: '100vh',
          backgroundColor: theme.palette.background.default,
        }}
      >
        {/* Header */}
        <Header />

        {/* Page content */}
        <Box
          sx={{
            flexGrow: 1,
            padding: 3,
            paddingTop: 2,
          }}
        >
          {children}
        </Box>
      </Box>
    </Box>
  )
}

export default Layout
