#!/usr/bin/env python3
"""
Direct upload test script to verify PDF upload functionality.
This bypasses the frontend and tests the backend directly.
"""

import requests
import json
import time
import sys
import os

# Configuration
BACKEND_URL = "http://localhost:9876"
TEST_FILE_PATH = r"C:\Users\<USER>\August N\data\test_medical_research.txt"

def test_upload_functionality():
    """Test the upload functionality directly."""
    print("🔍 Testing Upload Functionality Directly")
    print("=" * 50)
    
    # Test 1: Check if backend is running
    print("\n1. Testing backend connection...")
    try:
        response = requests.get(f"{BACKEND_URL}/health", timeout=5)
        print(f"✅ Backend is running! Status: {response.status_code}")
    except Exception as e:
        print(f"❌ Backend not accessible: {e}")
        return False
    
    # Test 2: Check if file exists
    print(f"\n2. Checking if test file exists: {TEST_FILE_PATH}")
    if os.path.exists(TEST_FILE_PATH):
        print(f"✅ File exists! Size: {os.path.getsize(TEST_FILE_PATH)} bytes")
    else:
        print(f"❌ File not found: {TEST_FILE_PATH}")
        return False
    
    # Test 3: Test OCR upload endpoint
    print("\n3. Testing OCR upload endpoint...")
    try:
        upload_data = {
            "file_path": TEST_FILE_PATH,
            "optimize_medical": True,
            "store_in_pinecone": True
        }
        
        response = requests.post(
            f"{BACKEND_URL}/api/ocr/upload-file",
            json=upload_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            document_id = result.get('document_id')
            print(f"✅ OCR upload successful!")
            print(f"   📄 Document ID: {document_id}")
            print(f"   📊 Status: {result.get('status')}")
            print(f"   ⏱️ Processing time: {result.get('processing_time')} seconds")
            
            # Test 4: Track progress
            print(f"\n4. Tracking upload progress...")
            for i in range(10):
                try:
                    progress_response = requests.get(
                        f"{BACKEND_URL}/api/ocr/progress/{document_id}",
                        timeout=5
                    )
                    
                    if progress_response.status_code == 200:
                        progress_data = progress_response.json()
                        print(f"   [{i+1}] Progress: {progress_data.get('progress', 0)}% - {progress_data.get('step', 'Processing...')}")
                        
                        if progress_data.get('vectors_created', 0) > 0:
                            print(f"       ✅ Vectors created: {progress_data.get('vectors_created')}")
                        
                        if progress_data.get('pages', 0) > 0:
                            print(f"       📄 Pages processed: {progress_data.get('pages')}")
                        
                        if progress_data.get('progress', 0) == 100:
                            print(f"       🎉 Upload complete!")
                            break
                    else:
                        print(f"   [{i+1}] Progress check failed: {progress_response.status_code}")
                    
                    time.sleep(2)
                    
                except Exception as e:
                    print(f"   [{i+1}] Progress error: {e}")
            
            return True
            
        else:
            print(f"❌ OCR upload failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ OCR upload error: {e}")
        return False

def test_document_upload():
    """Test the regular document upload endpoint."""
    print("\n" + "=" * 50)
    print("🔍 Testing Document Upload Endpoint")
    print("=" * 50)
    
    try:
        upload_data = {
            "file_path": TEST_FILE_PATH
        }
        
        response = requests.post(
            f"{BACKEND_URL}/api/documents/upload",
            json=upload_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            document_id = result.get('document_id')
            print(f"✅ Document upload successful!")
            print(f"   📄 Document ID: {document_id}")
            print(f"   📊 Status: {result.get('status')}")
            
            # Track progress
            print(f"\n📊 Tracking document upload progress...")
            for i in range(8):
                try:
                    progress_response = requests.get(
                        f"{BACKEND_URL}/api/documents/upload/progress/{document_id}",
                        timeout=5
                    )
                    
                    if progress_response.status_code == 200:
                        progress_data = progress_response.json()
                        print(f"   [{i+1}] Progress: {progress_data.get('progress', 0)}% - {progress_data.get('step', 'Processing...')}")
                        
                        if progress_data.get('vectors_created', 0) > 0:
                            print(f"       ✅ Vectors created: {progress_data.get('vectors_created')}")
                        
                        if progress_data.get('progress', 0) == 100:
                            print(f"       🎉 Document upload complete!")
                            break
                    
                    time.sleep(2)
                    
                except Exception as e:
                    print(f"   [{i+1}] Progress error: {e}")
            
            return True
            
        else:
            print(f"❌ Document upload failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Document upload error: {e}")
        return False

def check_vector_status():
    """Check the current vector database status."""
    print("\n" + "=" * 50)
    print("📊 Checking Vector Database Status")
    print("=" * 50)
    
    try:
        response = requests.get(f"{BACKEND_URL}/api/vectors/status", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Vector database status:")
            print(f"   📊 Total vectors: {data.get('total_vectors', 0)}")
            print(f"   📁 Namespaces: {len(data.get('namespaces', []))}")
            
            for namespace in data.get('namespaces', []):
                print(f"      - {namespace.get('name', 'default')}: {namespace.get('vector_count', 0)} vectors")
            
            return data.get('total_vectors', 0)
        else:
            print(f"❌ Vector status check failed: {response.status_code}")
            return 0
            
    except Exception as e:
        print(f"❌ Vector status error: {e}")
        return 0

if __name__ == "__main__":
    print("🚀 Direct Upload Test Script")
    print("This script tests the upload functionality directly, bypassing the frontend.")
    print("It will help identify where the upload process is failing.")
    
    # Check initial vector count
    initial_vectors = check_vector_status()
    
    # Test OCR upload
    ocr_success = test_upload_functionality()
    
    # Test document upload
    doc_success = test_document_upload()
    
    # Check final vector count
    final_vectors = check_vector_status()
    
    print("\n" + "=" * 50)
    print("📋 SUMMARY")
    print("=" * 50)
    print(f"✅ OCR Upload: {'SUCCESS' if ocr_success else 'FAILED'}")
    print(f"✅ Document Upload: {'SUCCESS' if doc_success else 'FAILED'}")
    print(f"📊 Vector Count Change: {initial_vectors} → {final_vectors} (+{final_vectors - initial_vectors})")
    
    if ocr_success or doc_success:
        print("\n🎯 CONCLUSION: Backend upload functionality is WORKING!")
        print("   The issue is likely in the frontend connection or UI interaction.")
        print("   Try refreshing the browser and checking the browser console for errors.")
    else:
        print("\n❌ CONCLUSION: Backend upload functionality has issues.")
        print("   Check the backend logs for detailed error information.")
