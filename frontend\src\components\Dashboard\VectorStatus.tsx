import React, { useState, useEffect } from 'react'
import {
  Box,
  Paper,
  Typography,
  Chip,
  LinearProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Tooltip,
  IconButton
} from '@mui/material'
import {
  Storage as StorageIcon,
  CloudUpload as UploadIcon,
  Refresh as RefreshIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Info as InfoIcon
} from '@mui/icons-material'
import { motion } from 'framer-motion'
import { apiService } from '../../services/api'

interface VectorStatusProps {
  onRefresh?: () => void
}

interface VectorStatus {
  total_vectors: number
  namespaces: {
    [key: string]: {
      vector_count: number
    }
  }
  recent_uploads: Array<{
    filename: string
    upload_time: string
    status: string
    vectors_created: number
  }>
  embedding_status: string
  last_updated: string
}

const VectorStatus: React.FC<VectorStatusProps> = ({ onRefresh }) => {
  const [vectorStatus, setVectorStatus] = useState<VectorStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false)
  const [uploadPath, setUploadPath] = useState('')
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<{
    progress: number
    status: string
    vectorsCreated: number
    namespace: string
    message: string
  } | null>(null)

  const fetchVectorStatus = async () => {
    try {
      setLoading(true)
      const data = await apiService.get<VectorStatus>('/vectors/status')
      setVectorStatus(data)
      setError(null)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch vector status')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchVectorStatus()
    // Refresh every 30 seconds
    const interval = setInterval(fetchVectorStatus, 30000)
    return () => clearInterval(interval)
  }, [])

  const handleRefresh = () => {
    fetchVectorStatus()
    onRefresh?.()
  }

  const handleUpload = async () => {
    if (!uploadPath.trim()) return

    try {
      setUploading(true)
      setUploadProgress({
        progress: 0,
        status: 'Starting upload...',
        vectorsCreated: 0,
        namespace: 'default',
        message: 'Initializing document processing...'
      })

      // Start the upload
      const result = await apiService.post('/documents/upload', { file_path: uploadPath })
      const documentId = result.document_id

      // Poll for real progress updates
      const pollProgress = async () => {
        try {
          const progressData = await apiService.get(`/documents/upload/progress/${documentId}`)

          setUploadProgress({
            progress: progressData.progress,
            status: progressData.status,
            vectorsCreated: progressData.vectors_created,
            namespace: progressData.namespace,
            message: progressData.step
          })

          // Continue polling if not complete
          if (progressData.status === 'processing' && progressData.progress < 100) {
            setTimeout(pollProgress, 1000) // Poll every second
          } else if (progressData.status === 'completed') {
            // Show success for a moment, then close
            setTimeout(() => {
              setUploadDialogOpen(false)
              setUploadPath('')
              setUploadProgress(null)
              setUploading(false)
              fetchVectorStatus() // Refresh to show new data
            }, 3000)
          }
        } catch (pollError) {
          console.error('Error polling progress:', pollError)
          setUploadProgress(prev => prev ? {
            ...prev,
            status: 'Error',
            message: 'Failed to get progress updates'
          } : null)
          setUploading(false)
        }
      }

      // Start polling after a short delay
      setTimeout(pollProgress, 500)

    } catch (err) {
      setUploadProgress({
        progress: 0,
        status: 'Error',
        vectorsCreated: 0,
        namespace: '',
        message: `Upload failed: ${err instanceof Error ? err.message : 'Unknown error'}`
      })
      setUploading(false)
    }
  }

  const getEmbeddingStatusColor = (status: string) => {
    switch (status) {
      case 'operational': return 'success'
      case 'mock_fallback': return 'warning'
      case 'failed': return 'error'
      default: return 'default'
    }
  }

  const getEmbeddingStatusIcon = (status: string) => {
    switch (status) {
      case 'operational': return <CheckIcon />
      case 'mock_fallback': return <WarningIcon />
      case 'failed': return <ErrorIcon />
      default: return <InfoIcon />
    }
  }

  if (loading && !vectorStatus) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <CircularProgress />
        <Typography variant="body2" sx={{ mt: 2 }}>
          Loading vector status...
        </Typography>
      </Paper>
    )
  }

  if (error) {
    return (
      <Paper sx={{ p: 3 }}>
        <Alert severity="error" action={
          <Button color="inherit" size="small" onClick={handleRefresh}>
            Retry
          </Button>
        }>
          {error}
        </Alert>
      </Paper>
    )
  }

  if (!vectorStatus) return null

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Paper sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Vector Database Status
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              startIcon={<UploadIcon />}
              onClick={() => setUploadDialogOpen(true)}
              size="small"
            >
              Upload Document
            </Button>
            <Tooltip title="Refresh Status">
              <IconButton onClick={handleRefresh} size="small">
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
          {/* Top Row */}
          <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 3 }}>
            {/* Vector Statistics */}
            <Box sx={{ flex: 1 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                Vector Statistics
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <StorageIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h4" sx={{ fontWeight: 600, mr: 1 }}>
                  {vectorStatus.total_vectors.toLocaleString()}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  total vectors
                </Typography>
              </Box>

              {/* Namespace Breakdown */}
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Namespace Distribution:
              </Typography>
              {Object.entries(vectorStatus.namespaces).map(([namespace, data]) => (
                <Box key={namespace} sx={{ mb: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                    <Typography variant="body2">
                      {namespace || 'default'}
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      {data.vector_count.toLocaleString()}
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={(data.vector_count / vectorStatus.total_vectors) * 100}
                    sx={{ height: 6, borderRadius: 3 }}
                  />
                </Box>
              ))}
            </Box>

            {/* Embedding Status */}
            <Box sx={{ flex: 1 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                Embedding Status
              </Typography>
              <Chip
                icon={getEmbeddingStatusIcon(vectorStatus.embedding_status)}
                label={vectorStatus.embedding_status.replace('_', ' ').toUpperCase()}
                color={getEmbeddingStatusColor(vectorStatus.embedding_status)}
                sx={{ mb: 2 }}
              />

              {vectorStatus.embedding_status === 'mock_fallback' && (
                <Alert severity="warning" sx={{ mb: 2 }}>
                  Using mock embeddings. Ollama model not available. Search results may not be accurate.
                </Alert>
              )}

              <Typography variant="body2" color="text.secondary">
                Last Updated: {new Date(vectorStatus.last_updated).toLocaleString()}
              </Typography>
            </Box>
          </Box>

          {/* Recent Uploads */}
          <Box>
            <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
              Recent Document Uploads
            </Typography>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Filename</TableCell>
                    <TableCell>Upload Time</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell align="right">Vectors Created</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {vectorStatus.recent_uploads.map((upload, index) => (
                    <TableRow key={index}>
                      <TableCell>{upload.filename}</TableCell>
                      <TableCell>
                        {new Date(upload.upload_time).toLocaleString()}
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={upload.status}
                          color={upload.status === 'processed' ? 'success' : 'warning'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell align="right">
                        {upload.vectors_created.toLocaleString()}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        </Box>

        {/* Upload Dialog */}
        <Dialog open={uploadDialogOpen} onClose={() => !uploading && setUploadDialogOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Upload Document</DialogTitle>
          <DialogContent>
            <TextField
              autoFocus
              margin="dense"
              label="File Path"
              fullWidth
              variant="outlined"
              value={uploadPath}
              onChange={(e) => setUploadPath(e.target.value)}
              placeholder="e.g., C:\path\to\document.pdf"
              helperText="Enter the full path to the document file"
              disabled={uploading}
            />

            {uploadProgress && (
              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle2" sx={{ mb: 1 }}>
                  {uploadProgress.status}
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={uploadProgress.progress}
                  sx={{ mb: 2, height: 8, borderRadius: 4 }}
                />
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  {uploadProgress.message}
                </Typography>

                {uploadProgress.vectorsCreated > 0 && (
                  <Box sx={{ mt: 2, p: 2, bgcolor: 'success.light', borderRadius: 1 }}>
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      ✅ Vectors Created: {uploadProgress.vectorsCreated}
                    </Typography>
                    <Typography variant="body2">
                      📁 Namespace: {uploadProgress.namespace}
                    </Typography>
                  </Box>
                )}
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => {
                setUploadDialogOpen(false)
                setUploadProgress(null)
                setUploadPath('')
              }}
              disabled={uploading}
            >
              {uploadProgress?.progress === 100 ? 'Close' : 'Cancel'}
            </Button>
            <Button
              onClick={handleUpload}
              variant="contained"
              disabled={!uploadPath.trim() || uploading}
              startIcon={uploading ? <CircularProgress size={16} /> : <UploadIcon />}
            >
              {uploading ? 'Processing...' : 'Upload'}
            </Button>
          </DialogActions>
        </Dialog>
      </Paper>
    </motion.div>
  )
}

export default VectorStatus
