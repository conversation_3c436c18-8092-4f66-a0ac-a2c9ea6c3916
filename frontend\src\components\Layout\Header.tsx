import React, { useState } from 'react'
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  IconButton,
  Badge,
  Menu,
  MenuItem,
  Box,
  InputBase,
  useTheme as useMuiTheme,
  useMediaQuery,
  Avatar,
  Divider,
  ListItemIcon,
  ListItemText,
} from '@mui/material'
import {
  Menu as MenuIcon,
  Search as SearchIcon,
  Notifications as NotificationsIcon,
  Brightness4 as DarkModeIcon,
  Brightness7 as LightModeIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material'
import { alpha } from '@mui/material/styles'

import { useAppSelector, useAppDispatch } from '../../store'
import { toggleSidebar, setSearchQuery } from '../../store/slices/uiSlice'

const Header: React.FC = () => {
  const muiTheme = useMuiTheme()
  const isMobile = useMediaQuery(muiTheme.breakpoints.down('md'))

  // Mock user data for display
  const user = { name: 'User', email: '<EMAIL>', avatar: null }

  const dispatch = useAppDispatch()
  const { notifications, searchQuery } = useAppSelector((state) => state.ui)

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [notificationAnchor, setNotificationAnchor] = useState<null | HTMLElement>(null)

  const unreadNotifications = notifications.filter(n => !n.read).length

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleProfileMenuClose = () => {
    setAnchorEl(null)
  }

  const handleNotificationMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setNotificationAnchor(event.currentTarget)
  }

  const handleNotificationMenuClose = () => {
    setNotificationAnchor(null)
  }

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSearchQuery(event.target.value))
  }

  const handleToggleDarkMode = () => {
    // Simple theme toggle - you can implement this later if needed
    console.log('Theme toggle clicked')
  }

  const handleToggleSidebar = () => {
    dispatch(toggleSidebar())
  }

  return (
    <AppBar
      position="sticky"
      elevation={0}
      sx={{
        backgroundColor: muiTheme.palette.background.paper,
        borderBottom: `1px solid ${muiTheme.palette.divider}`,
        color: muiTheme.palette.text.primary,
      }}
    >
      <Toolbar sx={{ justifyContent: 'space-between' }}>
        {/* Left section */}
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {isMobile && (
            <IconButton
              edge="start"
              color="inherit"
              aria-label="menu"
              onClick={handleToggleSidebar}
              sx={{ mr: 2 }}
            >
              <MenuIcon />
            </IconButton>
          )}

          {/* Search bar */}
          <Box
            sx={{
              position: 'relative',
              borderRadius: muiTheme.shape.borderRadius,
              backgroundColor: alpha(muiTheme.palette.common.black, 0.05),
              '&:hover': {
                backgroundColor: alpha(muiTheme.palette.common.black, 0.08),
              },
              marginLeft: 0,
              width: '100%',
              [muiTheme.breakpoints.up('sm')]: {
                marginLeft: muiTheme.spacing(1),
                width: 'auto',
              },
            }}
          >
            <Box
              sx={{
                padding: muiTheme.spacing(0, 2),
                height: '100%',
                position: 'absolute',
                pointerEvents: 'none',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <SearchIcon />
            </Box>
            <InputBase
              placeholder="Search documents, queries..."
              value={searchQuery}
              onChange={handleSearchChange}
              sx={{
                color: 'inherit',
                '& .MuiInputBase-input': {
                  padding: muiTheme.spacing(1, 1, 1, 0),
                  paddingLeft: `calc(1em + ${muiTheme.spacing(4)})`,
                  transition: muiTheme.transitions.create('width'),
                  width: '100%',
                  [muiTheme.breakpoints.up('md')]: {
                    width: '20ch',
                    '&:focus': {
                      width: '30ch',
                    },
                  },
                },
              }}
            />
          </Box>
        </Box>

        {/* Right section */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {/* Dark mode toggle */}
          <IconButton
            color="inherit"
            onClick={handleToggleDarkMode}
            aria-label="toggle dark mode"
          >
            <DarkModeIcon />
          </IconButton>

          {/* Notifications */}
          <IconButton
            color="inherit"
            onClick={handleNotificationMenuOpen}
            aria-label="notifications"
          >
            <Badge badgeContent={unreadNotifications} color="error">
              <NotificationsIcon />
            </Badge>
          </IconButton>

          {/* Profile menu */}
          <IconButton
            edge="end"
            aria-label="account"
            aria-controls="profile-menu"
            aria-haspopup="true"
            onClick={handleProfileMenuOpen}
            color="inherit"
          >
            <Avatar sx={{ width: 32, height: 32, bgcolor: muiTheme.palette.primary.main }}>
              {user?.avatar || user?.name?.charAt(0) || 'U'}
            </Avatar>
          </IconButton>
        </Box>
      </Toolbar>

      {/* Profile Menu */}
      <Menu
        id="profile-menu"
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleProfileMenuClose}
        onClick={handleProfileMenuClose}
        PaperProps={{
          elevation: 3,
          sx: {
            overflow: 'visible',
            filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
            mt: 1.5,
            '& .MuiAvatar-root': {
              width: 32,
              height: 32,
              ml: -0.5,
              mr: 1,
            },
          },
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem>
          <Avatar sx={{ bgcolor: muiTheme.palette.primary.main }}>
            {user?.avatar || user?.name?.charAt(0) || 'U'}
          </Avatar>
          <Box>
            <Typography variant="subtitle2">{user?.name || 'User'}</Typography>
            <Typography variant="caption" color="text.secondary">
              {user?.email || '<EMAIL>'}
            </Typography>
          </Box>
        </MenuItem>
        <Divider />
        <MenuItem>
          <ListItemIcon>
            <SettingsIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Settings</ListItemText>
        </MenuItem>
      </Menu>

      {/* Notifications Menu */}
      <Menu
        id="notifications-menu"
        anchorEl={notificationAnchor}
        open={Boolean(notificationAnchor)}
        onClose={handleNotificationMenuClose}
        PaperProps={{
          elevation: 3,
          sx: {
            overflow: 'visible',
            filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
            mt: 1.5,
            maxHeight: 400,
            width: 320,
          },
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="h6">Notifications</Typography>
        </Box>
        <Divider />
        {notifications.length === 0 ? (
          <MenuItem>
            <Typography variant="body2" color="text.secondary">
              No notifications
            </Typography>
          </MenuItem>
        ) : (
          notifications.slice(0, 5).map((notification) => (
            <MenuItem key={notification.id}>
              <Box sx={{ width: '100%' }}>
                <Typography variant="subtitle2" noWrap>
                  {notification.title}
                </Typography>
                <Typography variant="body2" color="text.secondary" noWrap>
                  {notification.message}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {new Date(notification.timestamp).toLocaleTimeString()}
                </Typography>
              </Box>
            </MenuItem>
          ))
        )}
      </Menu>
    </AppBar>
  )
}

export default Header
