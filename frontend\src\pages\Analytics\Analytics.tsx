import React, { useEffect, useState } from 'react'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
} from '@mui/material'
import {
  TrendingUp,
  Speed,
  Storage,
  Search,
  CheckCircle,
  Error,
  Warning,
} from '@mui/icons-material'
import { motion } from 'framer-motion'
import { apiService } from '../../services/api'

interface AnalyticsData {
  stats: Array<{
    title: string
    value: string
    change: string
    trend: string
    icon: string
    color: string
  }>
  activities: Array<{
    id: string
    type: string
    description: string
    timestamp: string
    status: string
  }>
  performance: {
    avgResponseTime: string
    successRate: string
    totalQueries: number
    activeModels: number
  }
}

const Analytics: React.FC = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        setLoading(true)
        setError(null)
        const data = await apiService.get<AnalyticsData>('/analytics/dashboard')
        setAnalyticsData(data)
      } catch (err: any) {
        console.error('Error fetching analytics:', err)
        setError(err.message || 'Failed to load analytics data')
      } finally {
        setLoading(false)
      }
    }

    fetchAnalytics()

    // Refresh data every 30 seconds
    const interval = setInterval(fetchAnalytics, 30000)
    return () => clearInterval(interval)
  }, [])

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'success':
      case 'completed':
        return <CheckCircle color="success" />
      case 'error':
      case 'failed':
        return <Error color="error" />
      case 'warning':
      case 'processing':
        return <Warning color="warning" />
      default:
        return <CheckCircle color="success" />
    }
  }

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString()
  }

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
      </Box>
    )
  }

  if (error) {
    return (
      <Box p={3}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      </Box>
    )
  }

  return (
    <Box>
      {/* Page Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Analytics Dashboard
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Real-time insights into system performance and usage patterns
          </Typography>
        </Box>
      </motion.div>

      {/* Performance Metrics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Speed sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                <Typography variant="h6">
                  {analyticsData?.performance?.avgResponseTime || 'N/A'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Avg Response Time
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <TrendingUp sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
                <Typography variant="h6">
                  {analyticsData?.performance?.successRate || 'N/A'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Success Rate
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Search sx={{ fontSize: 40, color: 'info.main', mb: 1 }} />
                <Typography variant="h6">
                  {analyticsData?.performance?.totalQueries || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Queries
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Storage sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
                <Typography variant="h6">
                  {analyticsData?.performance?.activeModels || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Active Models
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </motion.div>

      {/* Recent Activities */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Recent System Activities
            </Typography>
            <List>
              {analyticsData?.activities?.slice(0, 10).map((activity, index) => (
                <ListItem key={activity.id} divider={index < 9}>
                  <ListItemIcon>
                    {getStatusIcon(activity.status)}
                  </ListItemIcon>
                  <ListItemText
                    primary={activity.description}
                    secondary={formatTimestamp(activity.timestamp)}
                  />
                  <Chip
                    label={activity.type}
                    size="small"
                    variant="outlined"
                    sx={{ ml: 1 }}
                  />
                </ListItem>
              )) || (
                <ListItem>
                  <ListItemText
                    primary="No recent activities"
                    secondary="System activities will appear here"
                  />
                </ListItem>
              )}
            </List>
          </CardContent>
        </Card>
      </motion.div>
    </Box>
  )
}

export default Analytics
