import React from 'react'
import {
  <PERSON>,
  Typography,
  CircularProgress,
  Alert,
} from '@mui/material'
import {
  Description as DocumentIcon,
  Search as QueryIcon,
  Speed as PerformanceIcon,
  CloudUpload as UploadIcon,
} from '@mui/icons-material'
import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'

import StatCard from '../../components/Dashboard/StatCard'
import { type SvgIconComponent } from '@mui/icons-material'; // Import the type for icon components

interface Stat {
  title: string;
  value: string;
  change: string;
  trend: 'up' | 'down' | 'neutral';
  icon: string;
  color: string;
}

interface DashboardData {
  stats?: Stat[];
  activities?: any[]; // Define a proper type for activities if needed
  performance?: any; // Define a proper type for performance if needed
}

import RecentActivity from '../../components/Dashboard/RecentActivity'
import QuickActions from '../../components/Dashboard/QuickActions'
import SystemStatus from '../../components/Dashboard/SystemStatus'
import VectorStatus from '../../components/Dashboard/VectorStatus'
import ProcessingStatus from '../../components/ProcessingStatus/ProcessingStatus'
import { apiService } from '../../services/api'

const Dashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null)
  const [systemStatus, setSystemStatus] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true)
        setError(null)

        // Fetch dashboard analytics
        const analyticsData = await apiService.get('/analytics/dashboard')
        setDashboardData(analyticsData)

        // Fetch system status
        const statusData = await apiService.get('/system/status')
        setSystemStatus(statusData)

      } catch (err: any) {
        console.error('Error fetching dashboard data:', err)
        setError(err.message || 'Failed to load dashboard data')
      } finally {
        setLoading(false)
      }
    }

    fetchDashboardData()
  }, [])

  const getIconComponent = (iconName: string): SvgIconComponent => {
    switch (iconName) {
      case 'description': return DocumentIcon
      case 'search': return QueryIcon
      case 'speed': return PerformanceIcon
      case 'storage': return UploadIcon
      default: return DocumentIcon
    }
  }

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
      </Box>
    )
  }

  if (error) {
    return (
      <Box p={3}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      </Box>
    )
  }

  // Map API data to component format
  const stats = dashboardData?.stats?.map((stat: Stat, index: number) => ({
    title: stat.title,
    value: stat.value,
    change: stat.change,
    trend: stat.trend,
    icon: getIconComponent(stat.icon),
    color: stat.color,
  })) || []

  const recentActivities = dashboardData?.activities || []

  return (
    <Box>
      {/* Page Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Dashboard
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Welcome to August N - Natural Medicine AI Framework
          </Typography>
        </Box>
      </motion.div>

      {/* Stats Cards */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <Box sx={{
          display: 'grid',
          gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', md: '1fr 1fr 1fr 1fr' },
          gap: 3,
          mb: 4
        }}>
          {stats.map((stat, index) => (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3, delay: 0.1 + index * 0.05 }}
            >
              <StatCard {...stat} />
            </motion.div>
          ))}
        </Box>
      </motion.div>

      {/* Main Content */}
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
        {/* Top Row */}
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 3 }}>
          {/* Quick Actions */}
          <Box sx={{ flex: { md: '1 1 33%' } }}>
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              <QuickActions />
            </motion.div>
          </Box>

          {/* Recent Activity */}
          <Box sx={{ flex: { md: '1 1 67%' } }}>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: 0.3 }}
            >
              <RecentActivity activities={recentActivities} />
            </motion.div>
          </Box>
        </Box>

        {/* Middle Row */}
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 3 }}>
          {/* System Status */}
          <Box sx={{ flex: 1 }}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.4 }}
            >
              {systemStatus && <SystemStatus status={systemStatus} />}
            </motion.div>
          </Box>

          {/* Vector Status */}
          <Box sx={{ flex: 1 }}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.5 }}
            >
              <VectorStatus />
            </motion.div>
          </Box>
        </Box>

        {/* Bottom Row */}
        <Box>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.6 }}
          >
            <ProcessingStatus />
          </motion.div>
        </Box>
      </Box>
    </Box>
  )
}

export default Dashboard
