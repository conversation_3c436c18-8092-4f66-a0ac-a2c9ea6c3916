"""
Vector database endpoints for real Pinecone data.
"""

import time
from typing import Dict, Any, List, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from app.knowledge_base.pinecone.pinecone_client import PineconeClient
from app.core.logging import get_logger

router = APIRouter(tags=["Vectors"])
logger = get_logger("api.vectors")

# Initialize Pinecone client
pinecone_client = PineconeClient()

class VectorStatusResponse(BaseModel):
    """Vector status response model."""
    total_vectors: int
    namespaces: Dict[str, Any]
    recent_uploads: List[Dict[str, Any]]
    embedding_status: str
    last_updated: str

class VectorStatsResponse(BaseModel):
    """Vector statistics response model."""
    total_vector_count: int
    namespaces: Dict[str, Any]
    index_name: str
    dimension: int
    status: str

@router.get("/api/vectors/status", response_model=VectorStatusResponse)
async def get_vector_status():
    """
    Get detailed vector database status with real Pinecone data.
    
    Returns:
        VectorStatusResponse: Vector database status and statistics.
    """
    logger.info("Vector status request")
    
    try:
        # Get real Pinecone statistics
        stats = await pinecone_client.get_index_stats()
        
        # Determine embedding status
        embedding_status = "operational"
        if not stats:
            embedding_status = "error"
            stats = {"total_vector_count": 0, "namespaces": {}}
        
        # Get recent uploads (mock data for now, in production track this)
        recent_uploads = [
            {
                "filename": "Anemia In India.pdf",
                "upload_time": "2024-01-20T10:30:00Z",
                "status": "processed",
                "vectors_created": 25
            },
            {
                "filename": "Medical Research Study.pdf", 
                "upload_time": "2024-01-15T14:20:00Z",
                "status": "processed",
                "vectors_created": 32
            }
        ]
        
        return VectorStatusResponse(
            total_vectors=stats.get("total_vector_count", 0),
            namespaces=stats.get("namespaces", {}),
            recent_uploads=recent_uploads,
            embedding_status=embedding_status,
            last_updated=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Error getting vector status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/api/vectors/stats", response_model=VectorStatsResponse)
async def get_vector_stats():
    """
    Get basic vector database statistics.
    
    Returns:
        VectorStatsResponse: Vector database statistics.
    """
    logger.info("Vector stats request")
    
    try:
        # Get real Pinecone statistics
        stats = await pinecone_client.get_index_stats()
        
        if not stats:
            return VectorStatsResponse(
                total_vector_count=0,
                namespaces={},
                index_name="august-1024",
                dimension=1024,
                status="error"
            )
        
        return VectorStatsResponse(
            total_vector_count=stats.get("total_vector_count", 0),
            namespaces=stats.get("namespaces", {}),
            index_name="august-1024",
            dimension=1024,
            status="operational"
        )
        
    except Exception as e:
        logger.error(f"Error getting vector stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/api/vectors/health")
async def get_vector_health():
    """
    Get vector database health status.
    
    Returns:
        Dict[str, Any]: Health status.
    """
    logger.info("Vector health check request")
    
    try:
        # Test Pinecone connection
        stats = await pinecone_client.get_index_stats()
        
        if stats:
            return {
                "status": "healthy",
                "pinecone_connected": True,
                "total_vectors": stats.get("total_vector_count", 0),
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "status": "unhealthy",
                "pinecone_connected": False,
                "total_vectors": 0,
                "timestamp": datetime.now().isoformat()
            }
            
    except Exception as e:
        logger.error(f"Error checking vector health: {e}")
        return {
            "status": "error",
            "pinecone_connected": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
