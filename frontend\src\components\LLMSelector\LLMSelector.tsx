import React, { useState } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Button,

  Alert,
  CircularProgress,
  IconButton,
  Tooltip,
  Divider,
} from '@mui/material'
import {
  Settings as SettingsIcon,
  Refresh as Refresh<PERSON><PERSON>,
  CheckCircle as CheckIcon,
  Error as <PERSON>rrorIcon,
  Speed as SpeedIcon,
  AttachMoney as CostIcon,
  Memory as MemoryIcon,
} from '@mui/icons-material'
import { motion } from 'framer-motion'
import { useLLM, LLMProvider } from '../../contexts/LLMContext'

interface LLMSelectorProps {
  compact?: boolean
  showDetails?: boolean
}

const LLMSelector: React.FC<LLMSelectorProps> = ({ compact = false, showDetails = true }) => {
  const {
    providers,
    currentProvider,
    currentModel,
    setCurrentProvider,
    setCurrentModel,
    testConnection,
    refreshProviders,
    isLoading,
  } = useLLM()

  const [testingConnection, setTestingConnection] = useState(false)
  const [connectionResult, setConnectionResult] = useState<boolean | null>(null)

  const handleProviderChange = (providerId: string) => {
    setCurrentProvider(providerId)
    setConnectionResult(null)
  }

  const handleModelChange = (modelId: string) => {
    setCurrentModel(modelId)
  }

  const handleTestConnection = async () => {
    if (!currentProvider) return

    setTestingConnection(true)
    setConnectionResult(null)

    try {
      const result = await testConnection(currentProvider.id)
      setConnectionResult(result)
    } finally {
      setTestingConnection(false)
    }
  }

  const getProviderStatusIcon = (provider: LLMProvider) => {
    switch (provider.status) {
      case 'active':
        return <CheckIcon color="success" fontSize="small" />
      case 'error':
        return <ErrorIcon color="error" fontSize="small" />
      default:
        return <ErrorIcon color="disabled" fontSize="small" />
    }
  }

  const formatCost = (cost: number) => {
    return cost === 0 ? 'Free' : `$${cost}/1M tokens`
  }

  if (compact) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>Provider</InputLabel>
          <Select
            value={currentProvider?.id || ''}
            onChange={(e) => handleProviderChange(e.target.value)}
            label="Provider"
          >
            {providers.filter(p => p.isConfigured).map((provider) => (
              <MenuItem key={provider.id} value={provider.id}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {getProviderStatusIcon(provider)}
                  {provider.name}
                </Box>
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <FormControl size="small" sx={{ minWidth: 150 }}>
          <InputLabel>Model</InputLabel>
          <Select
            value={currentModel?.id || ''}
            onChange={(e) => handleModelChange(e.target.value)}
            label="Model"
            disabled={!currentProvider}
          >
            {currentProvider?.models.filter(m => m.isAvailable).map((model) => (
              <MenuItem key={model.id} value={model.id}>
                {model.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <Tooltip title="Test Connection">
          <IconButton
            size="small"
            onClick={handleTestConnection}
            disabled={!currentProvider || testingConnection}
          >
            {testingConnection ? <CircularProgress size={16} /> : <RefreshIcon />}
          </IconButton>
        </Tooltip>
      </Box>
    )
  }

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6" component="h2">
            LLM Configuration
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Refresh Providers">
              <IconButton size="small" onClick={refreshProviders} disabled={isLoading}>
                {isLoading ? <CircularProgress size={16} /> : <RefreshIcon />}
              </IconButton>
            </Tooltip>
            <Tooltip title="Provider Settings">
              <IconButton size="small">
                <SettingsIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 2 }}>
          <Box sx={{ flex: 1 }}>
            <FormControl fullWidth>
              <InputLabel>Provider</InputLabel>
              <Select
                value={currentProvider?.id || ''}
                onChange={(e) => handleProviderChange(e.target.value)}
                label="Provider"
              >
                {providers.map((provider) => (
                  <MenuItem key={provider.id} value={provider.id} disabled={!provider.isConfigured}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                      {getProviderStatusIcon(provider)}
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant="body2">{provider.name}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {provider.description}
                        </Typography>
                      </Box>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>

          <Box sx={{ flex: 1 }}>
            <FormControl fullWidth>
              <InputLabel>Model</InputLabel>
              <Select
                value={currentModel?.id || ''}
                onChange={(e) => handleModelChange(e.target.value)}
                label="Model"
                disabled={!currentProvider}
              >
                {currentProvider?.models.map((model) => (
                  <MenuItem key={model.id} value={model.id} disabled={!model.isAvailable}>
                    <Box sx={{ width: '100%' }}>
                      <Typography variant="body2">{model.name}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {model.description}
                      </Typography>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </Box>

        {showDetails && currentModel && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            transition={{ duration: 0.3 }}
          >
            <Box sx={{ mt: 3 }}>
              <Divider sx={{ mb: 2 }} />
              <Typography variant="subtitle2" gutterBottom>
                Model Details
              </Typography>
              
              <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2 }}>
                <Box sx={{ flex: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <MemoryIcon fontSize="small" color="primary" />
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        Context Window
                      </Typography>
                      <Typography variant="body2">
                        {currentModel.contextWindow.toLocaleString()} tokens
                      </Typography>
                    </Box>
                  </Box>
                </Box>

                <Box sx={{ flex: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <CostIcon fontSize="small" color="primary" />
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        Input Cost
                      </Typography>
                      <Typography variant="body2">
                        {formatCost(currentModel.inputCost)}
                      </Typography>
                    </Box>
                  </Box>
                </Box>

                <Box sx={{ flex: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <SpeedIcon fontSize="small" color="primary" />
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        Output Cost
                      </Typography>
                      <Typography variant="body2">
                        {formatCost(currentModel.outputCost)}
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </Box>

              <Box sx={{ mt: 2 }}>
                <Typography variant="caption" color="text.secondary" gutterBottom>
                  Capabilities
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                  {currentModel.capabilities.map((capability) => (
                    <Chip
                      key={capability}
                      label={capability}
                      size="small"
                      variant="outlined"
                      color="primary"
                    />
                  ))}
                </Box>
              </Box>
            </Box>
          </motion.div>
        )}

        <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', gap: 2 }}>
          <Button
            variant="outlined"
            onClick={handleTestConnection}
            disabled={!currentProvider || testingConnection}
            startIcon={testingConnection ? <CircularProgress size={16} /> : <RefreshIcon />}
          >
            {testingConnection ? 'Testing...' : 'Test Connection'}
          </Button>

          {connectionResult !== null && (
            <Alert severity={connectionResult ? 'success' : 'error'} sx={{ flexGrow: 1 }}>
              {connectionResult ? 'Connection successful!' : 'Connection failed. Check configuration.'}
            </Alert>
          )}
        </Box>
      </CardContent>
    </Card>
  )
}

export default LLMSelector
