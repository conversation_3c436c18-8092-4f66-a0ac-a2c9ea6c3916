"""
Pinecone integration for vector storage and retrieval.
"""

import os
import time
import json
import asyncio
from typing import Any, Dict, List, Optional, Union, Tuple
from datetime import datetime

import pinecone
from pinecone import Pinecone, ServerlessSpec
from loguru import logger

from app.config.settings import settings
from app.core.utils import safe_json_dumps, safe_json_loads
from app.api.models.pydantic_models import DocumentChunk, QueryResult

class PineconeClient:
    """
    Client for interacting with Pinecone vector database.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        environment: Optional[str] = None,
        index_name: Optional[str] = None,
        host: Optional[str] = None,
        dimension: Optional[int] = None
    ):
        """
        Initialize the Pinecone client.

        Args:
            api_key: Pinecone API key. If None, use the one from settings.
            environment: Pinecone environment. If None, use the one from settings.
            index_name: Pinecone index name. If None, use the one from settings.
            host: Pinecone host. If None, use the one from settings.
            dimension: Vector dimension. If None, use the one from settings.
        """
        self.logger = logger.bind(name="PineconeClient")

        # Use provided parameters or defaults from settings
        self.api_key = api_key or settings.PINECONE_API_KEY
        self.environment = environment or settings.PINECONE_ENVIRONMENT
        self.index_name = index_name or settings.PINECONE_INDEX_NAME
        self.host = host or settings.PINECONE_HOST
        self.dimension = dimension or settings.EMBEDDING_DIMENSION

        # Initialize Pinecone client
        self.pc = None
        self.index = None
        self.initialized = False

        # Initialize Pinecone client
        self._initialize()

    def _initialize(self) -> None:
        """Initialize the Pinecone client and index."""
        try:
            # Initialize Pinecone
            self.pc = Pinecone(api_key=self.api_key)

            # Check if index exists
            indexes = self.pc.list_indexes()

            if self.index_name not in [index.name for index in indexes]:
                self.logger.info(f"Creating Pinecone index: {self.index_name}")

                # Create index
                self.pc.create_index(
                    name=self.index_name,
                    dimension=self.dimension,
                    metric="cosine",
                    spec=ServerlessSpec(cloud="aws", region="us-east-1")
                )

                # Wait for index to be ready
                while not self.pc.describe_index(self.index_name).status["ready"]:
                    self.logger.info("Waiting for index to be ready...")
                    time.sleep(1)

            # Connect to index
            self.index = self.pc.Index(self.index_name)
            self.initialized = True
            self.logger.info(f"Connected to Pinecone index: {self.index_name}")

        except Exception as e:
            self.logger.error(f"Error initializing Pinecone: {e}")
            self.initialized = False

    def ensure_initialized(self) -> bool:
        """
        Ensure the Pinecone client is initialized.

        Returns:
            bool: True if initialized, False otherwise.
        """
        if not self.initialized:
            self._initialize()
        return self.initialized

    async def upsert_vectors(
        self,
        chunks: List[DocumentChunk],
        namespace: Optional[str] = None,
        batch_size: int = 100,
        max_retries: int = 3,
        retry_delay: float = 1.0
    ) -> Tuple[int, int]:
        """
        Upsert vectors to Pinecone.

        Args:
            chunks: List of document chunks to upsert.
            namespace: Namespace to upsert to.
            batch_size: Size of each batch.
            max_retries: Maximum number of retries.
            retry_delay: Delay between retries in seconds.

        Returns:
            Tuple[int, int]: Number of successful and failed upserts.
        """
        if not self.ensure_initialized():
            self.logger.error("Pinecone not initialized")
            return 0, len(chunks)

        self.logger.info(f"Upserting {len(chunks)} vectors to Pinecone")

        # Prepare vectors
        vectors = []
        for chunk in chunks:
            if not chunk.embedding:
                self.logger.warning(f"Chunk {chunk.id} has no embedding, skipping")
                continue

            # Convert metadata to JSON-serializable format
            metadata = chunk.metadata.dict()
            metadata = {k: v for k, v in metadata.items() if v is not None}

            # Convert datetime objects to strings and filter invalid types for Pinecone compatibility
            filtered_metadata = {}
            for key, value in metadata.items():
                if hasattr(value, 'isoformat'):  # datetime objects
                    filtered_metadata[key] = value.isoformat()
                elif isinstance(value, (str, int, float, bool)):
                    filtered_metadata[key] = value
                elif isinstance(value, list) and all(isinstance(item, str) for item in value):
                    filtered_metadata[key] = value
                elif isinstance(value, dict) and value:  # Only non-empty dicts, convert to string
                    filtered_metadata[key] = str(value)
                elif value is not None and not (isinstance(value, dict) and not value):  # Skip empty dicts and None
                    filtered_metadata[key] = str(value)

            metadata = filtered_metadata

            # Add document content to metadata
            metadata["text"] = chunk.content

            # Ensure embedding values are Python float (not numpy float64)
            embedding_values = [float(x) for x in chunk.embedding] if chunk.embedding else []

            vectors.append({
                "id": chunk.id,
                "values": embedding_values,
                "metadata": metadata
            })

        # Upsert vectors in batches
        successful = 0
        failed = 0

        for i in range(0, len(vectors), batch_size):
            batch = vectors[i:i + batch_size]

            for retry in range(max_retries):
                try:
                    self.index.upsert(vectors=batch, namespace=namespace)
                    successful += len(batch)
                    break
                except Exception as e:
                    self.logger.error(f"Error upserting batch {i // batch_size + 1}: {e}")
                    if retry < max_retries - 1:
                        self.logger.info(f"Retrying in {retry_delay} seconds...")
                        await asyncio.sleep(retry_delay)
                    else:
                        failed += len(batch)

        self.logger.info(f"Upserted {successful} vectors to Pinecone, {failed} failed")
        return successful, failed

    async def query_vectors(
        self,
        query_embedding: List[float],
        namespace: Optional[str] = None,
        top_k: int = 5,
        filter: Optional[Dict[str, Any]] = None
    ) -> List[QueryResult]:
        """
        Query vectors from Pinecone.

        Args:
            query_embedding: Query embedding.
            namespace: Namespace to query.
            top_k: Number of results to return.
            filter: Filter criteria.

        Returns:
            List[QueryResult]: List of query results.
        """
        if not self.ensure_initialized():
            self.logger.error("Pinecone not initialized")
            return []

        self.logger.info(f"Querying Pinecone with top_k={top_k}")

        try:
            # Query Pinecone
            query_response = self.index.query(
                vector=query_embedding,
                top_k=top_k,
                namespace=namespace,
                include_metadata=True,
                filter=filter
            )

            # Process results
            results = []
            for match in query_response.matches:
                # Extract content from metadata
                content = match.metadata.get("text", "")

                # Remove content from metadata
                metadata = {k: v for k, v in match.metadata.items() if k != "text"}

                result = QueryResult(
                    document_id=match.id,
                    content=content,
                    metadata=metadata,
                    similarity=match.score
                )

                results.append(result)

            self.logger.info(f"Found {len(results)} results")
            return results

        except Exception as e:
            self.logger.error(f"Error querying Pinecone: {e}")
            return []

    async def delete_vectors(
        self,
        ids: Optional[List[str]] = None,
        filter: Optional[Dict[str, Any]] = None,
        namespace: Optional[str] = None,
        delete_all: bool = False
    ) -> bool:
        """
        Delete vectors from Pinecone.

        Args:
            ids: List of vector IDs to delete.
            filter: Filter criteria.
            namespace: Namespace to delete from.
            delete_all: Whether to delete all vectors.

        Returns:
            bool: True if successful, False otherwise.
        """
        if not self.ensure_initialized():
            self.logger.error("Pinecone not initialized")
            return False

        try:
            if delete_all:
                self.logger.warning(f"Deleting all vectors from namespace: {namespace}")
                self.index.delete(delete_all=True, namespace=namespace)
                return True

            if ids:
                self.logger.info(f"Deleting {len(ids)} vectors by ID")
                self.index.delete(ids=ids, namespace=namespace)
                return True

            if filter:
                self.logger.info(f"Deleting vectors by filter: {filter}")
                self.index.delete(filter=filter, namespace=namespace)
                return True

            return False

        except Exception as e:
            self.logger.error(f"Error deleting vectors: {e}")
            return False

    async def list_namespaces(self) -> List[str]:
        """
        List all namespaces in the index.

        Returns:
            List[str]: List of namespace names.
        """
        if not self.ensure_initialized():
            self.logger.error("Pinecone not initialized")
            return []

        try:
            stats = self.index.describe_index_stats()
            namespaces = list(stats.namespaces.keys())
            self.logger.info(f"Found {len(namespaces)} namespaces")
            return namespaces

        except Exception as e:
            self.logger.error(f"Error listing namespaces: {e}")
            return []

    async def get_namespace_stats(self, namespace: Optional[str] = None) -> Dict[str, Any]:
        """
        Get statistics for a namespace.

        Args:
            namespace: Namespace to get statistics for.

        Returns:
            Dict[str, Any]: Namespace statistics.
        """
        if not self.ensure_initialized():
            self.logger.error("Pinecone not initialized")
            return {}

        try:
            stats = self.index.describe_index_stats()

            if namespace:
                if namespace in stats.namespaces:
                    return {
                        "namespace": namespace,
                        "vector_count": stats.namespaces[namespace].vector_count
                    }
                return {"namespace": namespace, "vector_count": 0}

            # Return stats for all namespaces
            namespace_stats = {}
            for ns, ns_stats in stats.namespaces.items():
                namespace_stats[ns] = {
                    "vector_count": ns_stats.vector_count
                }

            return {
                "total_vector_count": stats.total_vector_count,
                "namespaces": namespace_stats
            }

        except Exception as e:
            self.logger.error(f"Error getting namespace stats: {e}")
            return {}

    async def get_index_stats(self) -> Dict[str, Any]:
        """
        Get overall index statistics.

        Returns:
            Dict[str, Any]: Index statistics including total vector count and namespaces.
        """
        if not self.ensure_initialized():
            self.logger.error("Pinecone not initialized")
            return {}

        try:
            stats = self.index.describe_index_stats()

            # Return stats for all namespaces
            namespace_stats = {}
            for ns, ns_stats in stats.namespaces.items():
                namespace_stats[ns] = {
                    "vector_count": ns_stats.vector_count
                }

            return {
                "total_vector_count": stats.total_vector_count,
                "namespaces": namespace_stats
            }

        except Exception as e:
            self.logger.error(f"Error getting index stats: {e}")
            return {}
