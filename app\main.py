"""
Main FastAPI application entry point for the MAG project.
"""

import os
from typing import Dict, Any

from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
from fastapi.staticfiles import StaticFiles
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi

from app.config.settings import settings
from app.core.logging import setup_logging, get_logger
from app.api.routes import documents, search, ocr, query, health, analytics, vectors

# Set up logging
setup_logging()
logger = get_logger("main")

# Create FastAPI app
app = FastAPI(
    title=settings.API_TITLE,
    description=settings.API_DESCRIPTION,
    version=settings.API_VERSION,
    docs_url=None,  # Disable default docs
    redoc_url=None,  # Disable default redoc
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(health.router)  # Health at root level
app.include_router(documents.router, prefix="/api")
app.include_router(search.router, prefix="/api")
app.include_router(ocr.router, prefix="/api")
app.include_router(query.router, prefix="/api")
app.include_router(vectors.router)  # Vectors includes /api prefix
app.include_router(analytics.router)  # Analytics includes /api prefix

# Create static directory if it doesn't exist
os.makedirs("static", exist_ok=True)

# Mount static files (temporarily commented out)
# app.mount("/static", StaticFiles(directory="static"), name="static")

# Mount frontend static files if they exist (commented out for now)
# frontend_dist_path = "frontend/dist"
# if os.path.exists(frontend_dist_path):
#     app.mount("/", StaticFiles(directory=frontend_dist_path, html=True), name="frontend")

# Simple test endpoint
@app.get("/test")
async def test_endpoint():
    """Test endpoint to verify the server is working."""
    return {"status": "ok", "message": "Backend is working!"}

# Custom docs endpoints (temporarily commented out)
# @app.get("/docs", include_in_schema=False)
# async def custom_swagger_ui_html():
#     """
#     Custom Swagger UI endpoint.
#     """
#     return get_swagger_ui_html(
#         openapi_url="/openapi.json",
#         title=f"{settings.API_TITLE} - API Documentation",
#         swagger_js_url="/static/swagger-ui-bundle.js",
#         swagger_css_url="/static/swagger-ui.css",
#     )

# @app.get("/openapi.json", include_in_schema=False)
# async def get_open_api_endpoint():
#     """
#     OpenAPI endpoint.
#     """
#     return get_openapi(
#         title=settings.API_TITLE,
#         version=settings.API_VERSION,
#         description=settings.API_DESCRIPTION,
#         routes=app.routes,
#     )

@app.get("/")
async def root():
    """
    Root endpoint.
    """
    return {
        "name": settings.API_TITLE,
        "version": settings.API_VERSION,
        "description": settings.API_DESCRIPTION,
        "docs_url": "/docs",
    }

@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """
    Handle HTTP exceptions.
    """
    logger.error(f"HTTP error: {exc.status_code} - {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.detail},
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """
    Handle general exceptions.
    """
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error"},
    )

@app.on_event("startup")
async def startup_event():
    """
    Run on application startup.
    """
    logger.info(f"Starting {settings.API_TITLE} v{settings.API_VERSION}")
    logger.info(f"Environment: {settings.ENVIRONMENT}")
    logger.info(f"Debug mode: {settings.DEBUG}")

@app.on_event("shutdown")
async def shutdown_event():
    """
    Run on application shutdown.
    """
    logger.info(f"Shutting down {settings.API_TITLE}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=8876, reload=True)
