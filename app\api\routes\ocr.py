"""
OCR processing endpoints.
"""

import os
import time
import tempfile
import shutil
from typing import List, Optional, Dict, Any

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, UploadFile, File, Form
from fastapi.responses import JSONResponse
from pydantic import BaseModel, parse_obj_as

from app.api.models.pydantic_models import (
    Document, DocumentMetadata, OCRRequest, OCRResponse
)
from app.utils.ocr import process_pdf, RobustMistralOCR
from app.utils.document_utils import DocumentProcessor, DocumentRegistry
from app.knowledge_base.embedding.embedding_client import EmbeddingClient
from app.knowledge_base.pinecone.pinecone_client import PineconeClient
from app.core.logging import get_logger
from app.core.utils import generate_uuid, calculate_file_hash, safe_json_loads

router = APIRouter(tags=["OCR"])
logger = get_logger("api.ocr")

# Initialize clients
document_processor = DocumentProcessor()
document_registry = DocumentRegistry()
embedding_client = EmbeddingClient()
pinecone_client = PineconeClient()
ocr_processor = RobustMistralOCR()

class OCRFilePathRequest(BaseModel):
    """Request model for OCR file path uploads."""
    file_path: str
    optimize_medical: bool = True
    store_in_pinecone: bool = True

# Global progress tracking for OCR uploads
ocr_upload_progress: Dict[str, Dict] = {}

@router.post("/ocr/upload", response_model=OCRResponse)
async def upload_ocr_document(
    file: UploadFile = File(...),
    metadata: str = Form("{}"),
    optimize_medical: bool = Form(True),
    store_in_pinecone: bool = Form(True),
    background_tasks: BackgroundTasks = None
):
    """
    Upload a document for OCR processing and add it to the knowledge base.
    
    Args:
        file: The file to process.
        metadata: JSON string with metadata.
        optimize_medical: Whether to optimize for medical content.
        store_in_pinecone: Whether to store the results in Pinecone.
        background_tasks: Background tasks.
        
    Returns:
        OCRResponse: OCR processing response.
    """
    start_time = time.time()
    logger.info(f"OCR upload requested: {file.filename}")
    
    # Parse metadata
    try:
        metadata_dict = safe_json_loads(metadata, {})
        document_metadata = DocumentMetadata(**metadata_dict)
    except Exception as e:
        logger.error(f"Error parsing metadata: {e}")
        raise HTTPException(status_code=400, detail=f"Invalid metadata: {str(e)}")
    
    # Save file to temporary location
    temp_dir = tempfile.mkdtemp()
    temp_file_path = os.path.join(temp_dir, file.filename)
    
    try:
        with open(temp_file_path, "wb") as f:
            shutil.copyfileobj(file.file, f)
        
        # Calculate file hash
        file_hash = calculate_file_hash(temp_file_path)
        
        # Check if file is already registered
        existing_doc_id = document_registry.is_file_hash_registered(file_hash)
        if existing_doc_id:
            logger.warning(f"File already processed: {file.filename} (hash: {file_hash})")
            return JSONResponse(
                status_code=409,
                content={
                    "document_id": existing_doc_id,
                    "status": "already_exists",
                    "message": "This file has already been processed"
                }
            )
        
        # Generate document ID
        document_id = generate_uuid()
        
        # Add file info to metadata
        document_metadata.file_path = temp_file_path
        document_metadata.file_hash = file_hash
        document_metadata.file_type = os.path.splitext(file.filename)[1].lower()
        
        # Process OCR in background
        if background_tasks:
            background_tasks.add_task(
                process_ocr_background,
                document_id,
                temp_file_path,
                document_metadata,
                optimize_medical,
                store_in_pinecone
            )
            
            processing_time = time.time() - start_time
            logger.info(f"OCR processing initiated: {document_id}")
            
            return OCRResponse(
                document_id=document_id,
                pages=0,  # Will be updated in background
                chunks=0,  # Will be updated in background
                status="processing",
                processing_time=processing_time,
                stored_in_pinecone=store_in_pinecone
            )
        else:
            # Process synchronously for testing
            return await process_ocr_sync(
                document_id,
                temp_file_path,
                document_metadata,
                optimize_medical,
                store_in_pinecone
            )
            
    except Exception as e:
        logger.error(f"Error processing OCR: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error processing OCR: {str(e)}")
    finally:
        # Clean up temporary file
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)

@router.post("/ocr/upload-file", response_model=OCRResponse)
async def upload_ocr_document_from_file(
    request: OCRFilePathRequest,
    background_tasks: BackgroundTasks
):
    """
    Upload a document from file path for OCR processing and add it to the knowledge base.

    Args:
        request: OCR file path request.
        background_tasks: Background tasks.

    Returns:
        OCRResponse: OCR processing response.
    """
    start_time = time.time()
    logger.info(f"OCR file upload requested: {request.file_path}")

    try:
        # Check if file exists
        if not os.path.exists(request.file_path):
            raise HTTPException(status_code=404, detail=f"File not found: {request.file_path}")

        # Generate document ID
        document_id = generate_uuid()
        filename = os.path.basename(request.file_path)

        # Create metadata
        document_metadata = DocumentMetadata(
            source=request.file_path,
            title=filename,
            file_path=request.file_path,
            file_type=os.path.splitext(filename)[1].lower()
        )

        # Initialize progress tracking
        ocr_upload_progress[document_id] = {
            "status": "processing",
            "progress": 0,
            "step": "Starting OCR processing...",
            "vectors_created": 0,
            "namespace": "default",
            "chunks_processed": 0,
            "total_chunks": 0,
            "filename": filename,
            "pages": 0
        }

        # Process OCR in background
        background_tasks.add_task(
            process_ocr_background_with_progress,
            document_id,
            request.file_path,
            document_metadata,
            request.optimize_medical,
            request.store_in_pinecone
        )

        processing_time = time.time() - start_time
        logger.info(f"OCR processing initiated: {document_id} from {request.file_path}")

        return OCRResponse(
            document_id=document_id,
            pages=0,  # Will be updated in background
            chunks=0,  # Will be updated in background
            status="processing",
            processing_time=processing_time,
            stored_in_pinecone=request.store_in_pinecone
        )

    except Exception as e:
        logger.error(f"Error uploading OCR file: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/ocr/progress/{document_id}")
async def get_ocr_upload_progress(document_id: str):
    """
    Get OCR upload progress for a specific document.

    Args:
        document_id: Document ID to track.

    Returns:
        Dict: Progress information.
    """
    if document_id not in ocr_upload_progress:
        raise HTTPException(status_code=404, detail="OCR upload not found")

    return ocr_upload_progress[document_id]

@router.post("/ocr/process", response_model=Dict[str, Any])
async def process_ocr_only(
    file: UploadFile = File(...),
    optimize_medical: bool = Form(True)
):
    """
    Process a document with OCR and return the extracted text without storing it.
    
    Args:
        file: The file to process.
        optimize_medical: Whether to optimize for medical content.
        
    Returns:
        Dict[str, Any]: OCR processing results.
    """
    start_time = time.time()
    logger.info(f"OCR processing requested: {file.filename}")
    
    # Save file to temporary location
    temp_dir = tempfile.mkdtemp()
    temp_file_path = os.path.join(temp_dir, file.filename)
    
    try:
        with open(temp_file_path, "wb") as f:
            shutil.copyfileobj(file.file, f)
        
        # Process OCR
        documents = await process_pdf(temp_file_path, optimize_medical=optimize_medical)
        
        # Extract text
        results = []
        for doc in documents:
            results.append({
                "page": doc.metadata.page,
                "content": doc.content,
                "length": len(doc.content)
            })
        
        processing_time = time.time() - start_time
        logger.info(f"OCR processing completed: {len(results)} pages in {processing_time:.2f}s")
        
        return {
            "filename": file.filename,
            "pages": len(results),
            "results": results,
            "processing_time": processing_time
        }
            
    except Exception as e:
        logger.error(f"Error processing OCR: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error processing OCR: {str(e)}")
    finally:
        # Clean up temporary file
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)

async def process_ocr_background(
    document_id: str,
    file_path: str,
    metadata: DocumentMetadata,
    optimize_medical: bool,
    store_in_pinecone: bool
):
    """
    Process OCR in the background.
    
    Args:
        document_id: Document ID.
        file_path: Path to the file.
        metadata: Document metadata.
        optimize_medical: Whether to optimize for medical content.
        store_in_pinecone: Whether to store the results in Pinecone.
    """
    logger.info(f"Processing OCR in background: {document_id}")
    
    try:
        await process_ocr_sync(
            document_id,
            file_path,
            metadata,
            optimize_medical,
            store_in_pinecone
        )
    except Exception as e:
        logger.error(f"Error processing OCR in background: {e}", exc_info=True)

async def process_ocr_background_with_progress(
    document_id: str,
    file_path: str,
    metadata: DocumentMetadata,
    optimize_medical: bool,
    store_in_pinecone: bool
):
    """
    Process OCR in the background with detailed progress tracking.

    Args:
        document_id: Document ID.
        file_path: Path to the file.
        metadata: Document metadata.
        optimize_medical: Whether to optimize for medical content.
        store_in_pinecone: Whether to store the results in Pinecone.
    """
    logger.info(f"Processing OCR with progress tracking: {document_id}")

    try:
        # Update progress: Starting OCR
        ocr_upload_progress[document_id].update({
            "progress": 10,
            "step": "Processing PDF with Mistral OCR...",
            "status": "processing"
        })

        # Process OCR
        logger.info(f"Step 1: Processing PDF with OCR: {document_id}")
        documents = await process_pdf(file_path, optimize_medical=optimize_medical)
        logger.info(f"OCR extracted {len(documents)} pages from document: {document_id}")

        # Update progress: OCR complete, creating chunks
        ocr_upload_progress[document_id].update({
            "progress": 40,
            "step": f"OCR complete! Extracted {len(documents)} pages. Creating chunks...",
            "pages": len(documents)
        })

        # Create chunks and embeddings
        all_chunks = []
        for i, doc in enumerate(documents):
            # Set document ID and metadata
            doc.id = f"{document_id}_page_{doc.metadata.page}"
            doc.metadata = metadata.copy()
            doc.metadata.page = doc.metadata.page

            # Process document
            chunks = await document_processor.process_document(doc)

            # Update progress for each page
            progress = 40 + (30 * (i + 1) / len(documents))  # 40-70%
            ocr_upload_progress[document_id].update({
                "progress": int(progress),
                "step": f"Processing page {i+1}/{len(documents)} - created {len(chunks)} chunks",
                "chunks_processed": len(all_chunks) + len(chunks)
            })

            # Generate embeddings
            for chunk in chunks:
                embedding = await embedding_client.generate_embedding(chunk.content)
                chunk.embedding = embedding

            all_chunks.extend(chunks)

        # Update progress: Storing vectors
        ocr_upload_progress[document_id].update({
            "progress": 80,
            "step": f"Storing {len(all_chunks)} vectors in Pinecone...",
            "total_chunks": len(all_chunks)
        })

        # Store in Pinecone if requested
        if store_in_pinecone and all_chunks:
            await pinecone_client.upsert_vectors(all_chunks)

        # Update progress: Registering
        ocr_upload_progress[document_id].update({
            "progress": 95,
            "step": "Registering document..."
        })

        # Register document with summary content
        summary_content = f"OCR processed document with {len(documents)} pages and {len(all_chunks)} chunks"
        document_registry.register_document(
            Document(
                id=document_id,
                content=summary_content,
                metadata=metadata
            ),
            len(all_chunks)
        )

        # Final update: Complete
        ocr_upload_progress[document_id].update({
            "progress": 100,
            "step": "OCR processing complete!",
            "status": "completed",
            "vectors_created": len(all_chunks)
        })

        logger.info(f"OCR processing completed: {document_id} with {len(documents)} pages and {len(all_chunks)} chunks")

        # Log detailed results
        logger.info(f"✅ OCR Document processed successfully:")
        logger.info(f"   📄 Document ID: {document_id}")
        logger.info(f"   📁 Filename: {metadata.title}")
        logger.info(f"   📄 Pages processed: {len(documents)}")
        logger.info(f"   🔢 Chunks created: {len(all_chunks)}")
        logger.info(f"   🎯 Namespace: default")
        logger.info(f"   📊 Vectors stored in Pinecone: {len(all_chunks)}")

    except Exception as e:
        logger.error(f"Error processing OCR with progress: {e}", exc_info=True)
        # Update progress: Error
        if document_id in ocr_upload_progress:
            ocr_upload_progress[document_id].update({
                "progress": 0,
                "step": f"Error: {str(e)}",
                "status": "error"
            })

async def process_ocr_sync(
    document_id: str,
    file_path: str,
    metadata: DocumentMetadata,
    optimize_medical: bool,
    store_in_pinecone: bool
) -> OCRResponse:
    """
    Process OCR synchronously.
    
    Args:
        document_id: Document ID.
        file_path: Path to the file.
        metadata: Document metadata.
        optimize_medical: Whether to optimize for medical content.
        store_in_pinecone: Whether to store the results in Pinecone.
        
    Returns:
        OCRResponse: OCR processing response.
    """
    start_time = time.time()
    logger.info(f"Processing OCR synchronously: {document_id}")
    
    try:
        # Process OCR
        documents = await process_pdf(file_path, optimize_medical=optimize_medical)
        
        # Create chunks and embeddings
        all_chunks = []
        for doc in documents:
            # Set document ID and metadata
            doc.id = f"{document_id}_page_{doc.metadata.page}"
            doc.metadata = metadata.copy()
            doc.metadata.page = doc.metadata.page
            
            # Process document
            chunks = await document_processor.process_document(doc)
            
            # Generate embeddings
            for chunk in chunks:
                embedding = await embedding_client.generate_embedding(chunk.content)
                chunk.embedding = embedding
            
            all_chunks.extend(chunks)
        
        # Store in Pinecone if requested
        if store_in_pinecone and all_chunks:
            await pinecone_client.upsert_vectors(all_chunks)
        
        # Register document
        document_registry.register_document(
            Document(
                id=document_id,
                content="",  # Content is stored in chunks
                metadata=metadata
            ),
            len(all_chunks)
        )
        
        processing_time = time.time() - start_time
        logger.info(f"OCR processing completed: {document_id} with {len(documents)} pages and {len(all_chunks)} chunks in {processing_time:.2f}s")
        
        return OCRResponse(
            document_id=document_id,
            pages=len(documents),
            chunks=len(all_chunks),
            status="completed",
            processing_time=processing_time,
            stored_in_pinecone=store_in_pinecone
        )
            
    except Exception as e:
        logger.error(f"Error processing OCR: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error processing OCR: {str(e)}")
